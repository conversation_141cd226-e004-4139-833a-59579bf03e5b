import "./assets/css/global.scss";
import "@groupk/font-awesome-sdk/src/V6/css/pro-v4-font-face.min.css";
import "@groupk/font-awesome-sdk/src/V6/css/pro-v4-shims.min.css";
import "@groupk/font-awesome-sdk/src/V6/css/pro.min.css";
import "@groupk/vue3-interface-sdk/dist/style.css";
import {
	CrossDomainTracker,
	GoogleTracker,
	Router,
	RouterRoute,
	RouterStateConfigVersionModifier,
	VueRouteFactory,
	VueRouteOptions
} from "@groupk/horizon2-front";
import {GetInstance, setExecutionContext, SetInstance} from "@groupk/horizon2-core";
import {AnimatedRoute, AnimationBetweenRouteChange} from "../../../shared/routing/AnimatedRouter";
import {AuthStateModel} from "../../../shared/AuthStateModel";
import {Configuration, MainConfig} from "../../../shared/MainConfig";

let mainConfig: MainConfig = GetInstance(MainConfig);

mainConfig.init().then(function(configFromFile: Configuration) {
	startApp(configFromFile);
});

function startApp(config: Configuration) {
	let router = new Router<AnimatedRoute | RouterRoute>({
		prefix: "/",
		registerGlobalInterceptor: true,
		container: "mainRouterContainer",
		oldContainer: "oldRouterContainer",
		disableRouteTriggerIfIdentical: true,
	});
	if (window.innerWidth < 900) {
		new AnimationBetweenRouteChange(router);
	}


	const authStateModel = new AuthStateModel(config.mastodonApiEndpoint, true);
	SetInstance(AuthStateModel, authStateModel);

	router.addHook(new RouterStateConfigVersionModifier(13));

	let uuidRegex = /([0-9a-f]{8}\-[0-9a-f]{4}\-[0-9a-f]{4}\-[0-9a-f]{4}\-[0-9a-f]{12})/;
	const vueRouteOptions: Partial<VueRouteOptions> = {filters: {}};

	router.addRoute({regex: new RegExp(/establishment\//.source + uuidRegex.source + /\/login/.source), loader: () => import('./pages/login/login.vue').then((vue) => VueRouteFactory(vue.default, vueRouteOptions))});
	router.addRoute({regex: new RegExp(/establishment\//.source + uuidRegex.source + /\/password-reset/.source), loader: () => import('./pages/passwordReset/passwordReset.vue').then((vue) => VueRouteFactory(vue.default, vueRouteOptions))});
	router.addRoute({location: 'index', loader: () => import('./pages/login/login.vue').then((vue) => VueRouteFactory(vue.default, vueRouteOptions))});
	router.addRoute({location: 'login', loader: () => import('./pages/login/login.vue').then((vue) => VueRouteFactory(vue.default, vueRouteOptions))});
	router.addRoute({location: 'platforms', loader: () => import('./pages/platforms/platforms.vue').then((vue) => VueRouteFactory(vue.default, vueRouteOptions))});
	router.addRoute({location: 'password-reset', loader: () => import('./pages/passwordReset/passwordReset.vue').then((vue) => VueRouteFactory(vue.default, vueRouteOptions))});
	router.addRoute({location: 'signup', loader: () => import('./pages/signup/signup.vue').then((vue) => VueRouteFactory(vue.default, vueRouteOptions))});
	router.addRoute({location: 'not-found', loader: () => import('./pages/notFound/notFound.vue').then((vue) => VueRouteFactory(vue.default, vueRouteOptions))});

	if(config.develop) {
		setExecutionContext({
			debug: true
		});
	}

	if(mainConfig.configuration.gtagId) {
		GoogleTracker.instance.addTrackingId(mainConfig.configuration.gtagId);
		CrossDomainTracker.instance.registerTrackingProvider(GoogleTracker.instance);
	}

	SetInstance(Router, router);
	router.updateCurrentPageFromCurrentLocation();
}

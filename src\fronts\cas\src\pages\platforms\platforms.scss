#platforms-page {
    display: flex;
    flex-direction: column;
    gap: 40px;
    padding: 80px 40px;
    box-sizing: border-box;

    h1 {
        margin: 0;
        font-size: 26px;
        font-weight: 600;
        text-align: center;
    }

    .platforms {
        display: flex;
        flex-wrap: wrap;
        justify-content: flex-start;
        grid-gap: 20px;
        max-width: 1000px;
        margin: 0 auto;

        .platform {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            gap: 5px;
            text-decoration: none;
            color: black;
            background: var(--secondary-hover-color);
            border-radius: 8px;
            padding: 15px;
            height: 120px;
            aspect-ratio: 1/1;
            margin: 0 auto;

            &:hover {
                background: #ededed;
            }

            .logo {
                height: 60px;
            }

            .name {
                font-weight: 500;
                text-align: center;
            }
        }
    }
}
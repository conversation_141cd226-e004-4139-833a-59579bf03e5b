import {Component, Vue} from "vue-facing-decorator";
import {PlatformDescriptorApiOut} from "@groupk/mastodon-core";
import {AutoWired} from "@groupk/horizon2-core";
import {PlatformRepository} from "../../../../../shared/repositories/PlatformRepository";

@Component({
    components: {}
})
export default class platforms extends Vue {
    platforms!: PlatformDescriptorApiOut;
    loading: boolean = true;

    @AutoWired(PlatformRepository) private accessor platformRepository!: PlatformRepository;

    async mounted() {
        this.platforms = (await this.platformRepository.callContract('get', undefined, undefined)).success();
        this.loading = false;
    }
}
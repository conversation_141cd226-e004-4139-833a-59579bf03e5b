<script lang="ts" src="./platforms.ts"/>

<style lang="sass" scoped>
@import './platforms.scss'
</style>

<template>
    <div id="platforms-page" class="page">
        <div class="loading-container" v-if="loading">
            <div class="loader"></div>
        </div>
        <template v-else>
            <h1> Applications Weecop </h1>

            <div class="platforms">
                <a :href="'/login?platform=' + platform.id" class="platform" v-for="platform in platforms.fronts">
                    <img class="logo" :alt="platform.name" :src="platform.url + 'img/logo.svg'" />
                    <span class="name"> {{ platform.name }} </span>
                </a>
            </div>
        </template>

    </div>
</template>
<script lang="ts" src="./CustomerInfoFormComponent.ts" />

<style lang="sass" scoped>
@import './CustomerInfoFormComponent.scss'
</style>

<template>
    <div class="customer-info-form-component">
        <div class="form">
            <div class="establishment" v-if="!loading">
                <img :src="getEstablishmentImageUrl()??''" v-if="getEstablishmentImageUrl()"/>

                <span class="name">  {{ establishment.name }} </span>
            </div>

            <span class="title">    Recharger mon bracelet </span>

            <div class="input">
                <label> Prénom </label>
                <input v-model="manualData.firstname" type="text" placeholder="Prénom" />
            </div>

            <div class="input">
                <label> Nom </label>
                <input v-model="manualData.lastname" type="text" placeholder="Nom" />
            </div>

            <div class="input">
                <label> Email </label>
                <input autocomplete="email" spellcheck="false" type="email" v-model="manualData.email" placeholder="Email" />
            </div>

            <div class="input">
                <label> Code affiché sur le support Cashless </label>

                <input v-model="manualData.idPublic" type="text" @keydown.enter="validate()" />

<!--                <div class="info"> Où trouver le code ? </div>-->
            </div>

            <div class="form-error" v-if="error">
                <i class="fa-regular fa-circle-exclamation"></i>
                <div class="details">
                    {{ error }}
                </div>
            </div>

            <button
                class="add-button button"
                :class="{loading: validating, disabled: validating || manualData.firstname.length === 0 || manualData.lastname.length === 0 || manualData.email.length === 0 || manualData.idPublic.length === 0}"
                @click="validate()"
            >
                Continuer
            </button>
        </div>
    </div>
</template>
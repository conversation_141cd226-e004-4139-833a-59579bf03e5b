<script lang="ts" src="./kioskFunding.ts">
</script>

<style lang="sass">
@use './kioskFunding.scss' as *
</style>

<template>
    <div id="kiosk-founding-page" class="page">
        <filter-table-layout
            :header-parameters="headerParameters"
            :allowed-filters="allowedFilters"
            :applied-filters="appliedFilters"
            :table-columns="tableColumns"
            :filters="filters"
            :pagination="pagination"
            :drawer-opened="selectedKioskFunding !== null"
            :filter-parameters="filterParameters"
            :saved-filters="savedFilters"
            @changed-column-preferences="saveColumnPreferences($event)"
            @filters-changed="searchKiosksFunding($event);"
            @next="nextPage()"
            @previous="previousPage()"
            @save-filter="saveFilter($event)"
            @select-filter="selectFilter($event)"
            @delete-filter="deleteFilter($event)"
        >
            <template v-slot:top-extra-content>
                <div class="top-content" v-if="kiosksFunding.length === 0 && Object.keys(filters).length === 0">
                    <div class="banner">
                        <div class="left">
                            <div class="title"> Installez une borne automatique ! </div>
                            <div class="description"> Simplifiez vos opérations et améliorez l'efficacité avec une borne automatique. Contactez-nous dès maintenant pour la mettre en place ! </div>
                        </div>

                        <a href="https://littl.fr/masto-cashless-contact?source=kiosk-funding" target="_blank" class="button">
                            <i class="fa-regular fa-message-middle"></i>
                            Contact
                        </a>
                    </div>

                    <div class="cards">
                        <div class="card">
                            <img alt="Borne de rechargement" class="kiosk-image" src="/img/kiosk.svg" />
                            <div class="header">
                                <span class="title"> Borne de recharge autonome </span>
                                <span class="description">
                                    Offrez à vos clients une solution de recharge ultra-pratique avec notre borne automatique équipée de paiement intégré.
                                    Rechargez facilement, rapidement et en toute autonomie !
                                </span>
                            </div>
                        </div>
                        <div class="card">
                            <div class="animation-container">
                                <div class="stacked-transactions">
                                    <template  v-for="(transaction, index) of transactions">
                                        <div class="automatic-transaction" :class="'step-' + (transactions.length - index)" v-if="(transactions.length - index) < 5">
                                            <div class="left">
                                                <span class="hour"> {{ transaction.hour }} </span>
                                                <span class="name"> {{ transaction.kiosk }} </span>
                                            </div>
                                            <span class="amount"> +{{ transaction.amount }} </span>
                                        </div>
                                    </template>
                                </div>
                            </div>

                            <div class="header">
                                <span class="title"> Encaissement automatique </span>
                                <span class="description">
                                    Maximisez vos profits sans effort ! Notre borne, disponible 24h/24, gère jusqu'à 120 clients par heure.
                                    Dites adieu aux contraintes de personnel et bonjour à l'efficacité optimale.
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </template>

            <template v-if="!forbidden" v-slot:table-data>
                <tr class="table-dimmer" :class="{relative: kiosksFunding.length === 0}" v-if="loading">
                    <td colspan="100%">
                        <div class="dimmer">
                            <div class="loader-container">
                                <div class="loader"></div>
                            </div>
                        </div>
                    </td>
                </tr>
                <tr class="table-no-data" v-else-if="kiosksFunding.length === 0">
                    <td colspan="100%"> Vous transactions automatiques apparaîtront ici </td>
                </tr>
                <tr
                    v-else
                    v-for="kioskFunding in kiosksFunding"
                    :class="{selected: selectedKioskFunding && kioskFunding.uid === selectedKioskFunding.uid}"
                    @click="selectedKioskFunding = kioskFunding"
                >
                    <td :class="{'mobile-hidden': column.mobileHidden}" v-for="column in tableColumns.filter((column) => column.displayed)">
                        <template v-if="column.name === 'uid'"> {{ kioskFunding.uid.slice(-8) }} </template>
                        <template v-if="column.name === 'currency'">  {{ getCorrespondingCurrency(kioskFunding.cashlessCurrency).name }} ({{ getCorrespondingCurrency(kioskFunding.cashlessCurrency).symbol }})  </template>
                        <template v-if="column.name === 'fiatAmount'"> {{ $filters.Money(kioskFunding.fiatAmount) }} </template>
                        <template v-if="column.name === 'cashlessAmount'"> {{ $filters.Money(kioskFunding.cashlessAmount) }} </template>
                        <template v-if="column.name === 'creatorDevice'">
                            {{ kioskFunding.creatorDeviceUid.slice(-8) }}
                        </template>
                        <template v-if="column.name === 'profileUid'"> {{ kioskFunding.profileUid.slice(-8) }} </template>
                        <template v-if="column.name === 'fiatPaymentMethod'"> {{ kioskFunding.fiatPaymentMethod.slice(-8) }} </template>
                        <template v-if="column.name === 'chipPublicId'"> {{ kioskFunding.creditedChipPublicId ? $filters.Chip(kioskFunding.creditedChipPublicId) : '-' }} </template>
                        <template v-if="column.name === 'fiatPaymentStatus'">
                            <div class="label" v-if="kioskFunding.fiatPaymentStatus === 'S'"> Succès </div>
                            <div class="grey label" v-else-if="kioskFunding.fiatPaymentStatus === 'P'"> En attente </div>
                            <div class="red label" v-if="kioskFunding.fiatPaymentStatus === 'E'"> Erreur </div>
                        </template>
                        <template v-if="column.name === 'creationDatetime'">
                            {{ $filters.Date(kioskFunding.creationDatetime) }}
                            {{ $filters.Hour(kioskFunding.creationDatetime) }}
                        </template>
                        <template v-if="column.name === 'finalStateDatetime'">
                            {{ $filters.Date(kioskFunding.finalStateDatetime) }}
                            {{ $filters.Hour(kioskFunding.finalStateDatetime) }}
                        </template>
                    </td>
                </tr>
            </template>
            <template v-slot:right>
                <div v-if="!selectedKioskFunding" class="empty-right-panel">
                    <img src="../../assets/img/select-hint.svg" />
                    Cliquer sur un support pour <br/> le sélectionner
                </div>
                <div v-else class="selected-kiosk-funding">
                    <div class="close" @click="selectedKioskFunding = null">
                        <i class="fa-regular fa-xmark"></i>
                        <span> Fermer </span>
                    </div>

                    <div class="header">
                        <div class="left">
                            <h2> Rechargement N°{{ selectedKioskFunding.uid.slice(-8) }} </h2>
                        </div>
                    </div>

                    <div class="funding-details">
                        <div class="properties-table">
                            <div class="row">
                                <span class="title"> ID </span>
                                <span class="value">
                                    <hoverable-infos
                                        :data="{
                                            title: 'ID',
                                            description: selectedKioskFunding.uid
                                        }"
                                        :alignment="'LEFT'"
                                    ></hoverable-infos>

                                    {{ selectedKioskFunding.uid.slice(-8) }}
                                </span>
                            </div>
                            <div class="row">
                                <span class="title"> Devise </span>
                                <span class="value"> {{ getCorrespondingCurrency(selectedKioskFunding.cashlessCurrency).name }} ({{ getCorrespondingCurrency(selectedKioskFunding.cashlessCurrency).symbol }}) </span>
                            </div>
                            <div class="row">
                                <span class="title"> Montant Cashless </span>
                                <span class="value"> {{ $filters.Money(selectedKioskFunding.cashlessAmount) }} </span>
                            </div>
                            <div class="row">
                                <span class="title"> ID Monnaie Cashless </span>
                                <span class="value"> {{ selectedKioskFunding.cashlessCurrency }} </span>
                            </div>
                            <div class="row">
                                <span class="title"> Montant Fiat </span>
                                <span class="value"> {{ $filters.Money(selectedKioskFunding.fiatAmount) }} </span>
                            </div>
                            <div class="row">
                                <span class="title"> ID point de vente </span>
                                <span class="value">
                                    <hoverable-infos
                                        :data="{
                                                title: 'ID point de vente',
                                                description: selectedKioskFunding.profileUid
                                            }"
                                        :alignment="'LEFT'"
                                    ></hoverable-infos>
                                    {{ selectedKioskFunding.profileUid.slice(-8) }}
                                </span>
                            </div>
                            <div class="row">
                                <span class="title"> ID Appareil </span>
                                <span class="value">
                                     <hoverable-infos
                                         :data="{
                                            title: 'ID Appareil',
                                            description: selectedKioskFunding.creatorDeviceUid
                                        }"
                                         :alignment="'LEFT'"
                                     ></hoverable-infos>
                                    {{ selectedKioskFunding.creatorDeviceUid.slice(-8) }}
                                </span>
                            </div>
                            <div class="row">
                                <span class="title"> Date de création </span>
                                <span class="value">
                                    {{ $filters.Date(selectedKioskFunding.creationDatetime) }}
                                    {{ $filters.Hour(selectedKioskFunding.creationDatetime) }}
                                </span>
                            </div>
                            <div class="row">
                                <span class="title"> Date du statut finale </span>
                                <span class="value">
                                    {{ $filters.Date(selectedKioskFunding.finalStateDatetime) }}
                                    {{ $filters.Hour(selectedKioskFunding.finalStateDatetime) }}
                                </span>
                            </div>
                        </div>
                    </div>

                    <div v-if="selectedKioskFunding.cashlessTransactionUid" class="white button" @click="goToChipTransactions(selectedKioskFunding.cashlessTransactionUid)" style="white-space: nowrap">
                        <i class="fa-regular fa-arrow-up-right-from-square"></i>
                        Voir la transaction
                    </div>
                    <div v-else> Pas de transaction </div>
                </div>
            </template>
        </filter-table-layout>
    </div>
</template>
import {Component, Vue} from "vue-facing-decorator";
import {
	DropdownButtonAction,
	DropdownButtonComponent, DropdownValue,
	FilterTableLayoutComponent,
	ForbiddenMessageComponent, SavedFilter
} from "@groupk/vue3-interface-sdk";
import {
	CashlessHttpWalletContractSearchConfig, ChipModel, CurrencyApiOut, CustomerChipApiOut,
	SimpleProductApiOut,
	WalletApiOut, WalletInactiveReason,
	ApplicationPermission,
	EstablishmentAccountPermissionModel,
	CashlessHttpWalletContract, CashlessHttpCurrencyContract, CashlessHttpTransactionContractSearchConfig
} from "@groupk/mastodon-core";
import {ContentHeaderParameters, TableColumn, TablePagination, FilterParameters} from "@groupk/vue3-interface-sdk";
import {
	AutoWired, dehydrate, QueryFilterGroupClause, QueryOperator,
	ScopedUuid,
	TypedQuerySearch, Uuid,
	UuidUtils,
	VisualScopedUuid
} from "@groupk/horizon2-core";
import {TableColumnsRepository} from "../../../../../shared/repositories/TableColumnsRepository";
import {OptionBuilder} from "vue-facing-decorator/dist/optionBuilder";
import {uuidScopeEstablishment, UuidScopeEstablishment} from "@groupk/mastodon-core";
import {Router} from "@groupk/horizon2-front";
import SidebarStateListener from "../../../../../shared/utils/SidebarStateListener";
import {WalletsRepository} from "../../../../../shared/repositories/WalletsRepository";
import {CurrencyRepository} from "../../../../../shared/repositories/CurrencyRepository";
import {CustomerChipRepository} from "../../../../../shared/repositories/CustomerChipRepository";
import {UuidScopeCustomer_customer} from "@groupk/mastodon-core";
import {EstablishmentUrlBuilder} from "../../../../../shared/utils/EstablishmentUrlBuilder";
import WalletDeclareStateFormComponent
	from "../../components/WalletDeclareStateFormComponent/WalletDeclareStateFormComponent.vue";
import WalletNetworkTransactionFormComponent
	from "../../components/WalletNetworkTransactionFormComponent/WalletNetworkTransactionFormComponent.vue";
import DateUtils from "../../../../../shared/utils/DateUtils";
import ExportChoiceComponent, {ChosenExport} from "../../components/ExportChoiceComponent/ExportChoiceComponent.vue";
import {ExportFileType, MultiFormatExporter} from "../../../../../shared/utils/MultiFormatExporter";
import {MoneyFilter} from "../../../../../shared/filters/Money";
import {QueryAllowedFiltersToTypedQueryFilters, TypedQueryFilterGroup} from "@groupk/horizon2-core";
import {QueryFilterUtils} from "../../../../../shared/utils/QueryFilterUtils";
import {ComponentUtils} from "../../../../../shared/utils/ComponentUtils";
import {WalletDeclareStateFormComponentHasRequiredPermissions} from "../../components/WalletDeclareStateFormComponent/WalletDeclareStateFormComponent";
import {WalletNetworkTransactionFormComponentHasRequiredPermissions} from "../../components/WalletNetworkTransactionFormComponent/WalletNetworkTransactionFormComponent";
import {SavedFiltersRepository} from "../../../../../shared/repositories/SavedFiltersRepository";

@Component({
	components: {
		'filter-table-layout': FilterTableLayoutComponent,
		'wallet-declare-state-form': WalletDeclareStateFormComponent,
		'dropdown-button': DropdownButtonComponent,
		'wallet-network-transaction-form': WalletNetworkTransactionFormComponent,
		'export-choice-modal': ExportChoiceComponent,
		'forbidden-message': ForbiddenMessageComponent,
	}
})
export default class ProductsView extends Vue {
	establishmentUid!: VisualScopedUuid<UuidScopeEstablishment>;

	wallets: WalletApiOut[] = [];
	selectedWallet: WalletApiOut | null = null;
	selectedWalletCustomerChip: CustomerChipApiOut | null = null;
	currencies: CurrencyApiOut[] = [];

	customerChipHistory: Record<any, CustomerChipApiOut[]> = {};
	loadingCustomerChipHistory: boolean = false;

	showUnusableChipConfirmationModal: {
		reason: WalletInactiveReason,
		wallet: WalletApiOut
	} | null = null;
	showEmptyChipConfirmationModal: {
		type: 'CLEAR' | 'ENABLE',
		wallet: WalletApiOut
	} | null = null;
	loading: boolean = true;
	forbidden: boolean = false;
	disabledActions: string[] = [];

	headerParameters: ContentHeaderParameters = {
		header: 'Supports de paiement',
		subtitle: 'Liste des supports de paiments Cashless',
		actions: [{
			type: 'SIMPLE_ACTION',
			name: 'Exporter',
			icon: 'fa-regular fa-arrow-down-to-line',
			callback: this.toggleExportModal
		}],
		hideSearch: false,
		searchPlaceholder: 'Rechercher un support'
	}

	allowedFilters = CashlessHttpWalletContractSearchConfig;
	filters: TypedQuerySearch<typeof CashlessHttpWalletContractSearchConfig> = {};
	appliedFilters: TypedQuerySearch<typeof CashlessHttpWalletContractSearchConfig> = {};

	filterParameters: {
		[filterName: string]: FilterParameters
	} = {
		'balance': {
			translation: 'Solde',
			type: 'DECIMAL',
			transformFunction: QueryFilterUtils.priceTransformFunction(),
			validation: QueryFilterUtils.amountValidation
		},
		'currency': {
			translation: 'Devise',
			type: 'DROPDOWN',
			dropdownValues: []
		},
		'publicChipId': {
			translation: 'ID public de la puce',
			type: 'UNKNOWN'
		},
	}

	tableKey = 'cashless-wallets';
	tableColumns: TableColumn[] = [{
		title: 'ID public', name: 'publicId', displayed: true, mobileHidden: false
	}, {
		title: 'Devise', name: 'currency', displayed: true, mobileHidden: false
	}, {
		title: 'Solde', name: 'balance', displayed: true, mobileHidden: false
	}, {
		title: 'Statut', name: 'status', displayed: true, mobileHidden: false
	}, {
		title: 'KYC', name: 'kycLevel', displayed: false, mobileHidden: true
	}, {
		title: 'Date de création', name: 'creationDatetime', displayed: false, mobileHidden: true
	}, {
		title: '', name: 'action', displayed: true, mobileHidden: false
	}];

	pagination: TablePagination = {
		totalResults: 1,
		resultsPerPage: 50,
		currentPage: 1,
		estimateTotal: false
	}

	savedFilters: SavedFilter[] = [];

	showExportModal: boolean = false;
	downloadingExport: boolean = false;

	WalletInactiveReason = WalletInactiveReason;

	@AutoWired(WalletsRepository) accessor walletsRepository!: WalletsRepository
	@AutoWired(TableColumnsRepository) accessor tableColumnsRepository!: TableColumnsRepository
	@AutoWired(CurrencyRepository) accessor currencyRepository!: CurrencyRepository
	@AutoWired(SidebarStateListener) accessor sidebarStateListener!: SidebarStateListener
	@AutoWired(SavedFiltersRepository) accessor savedFiltersRepository!: SavedFiltersRepository
	@AutoWired(CustomerChipRepository) accessor customerChipRepository!: CustomerChipRepository
	@AutoWired(Router) accessor router!: Router

	constructor(optionBuilder: OptionBuilder, vueInstance: any) {
		super(optionBuilder, vueInstance);

		this.loading = true;

		this.sidebarStateListener.setHiddenSidebar(false);
		this.sidebarStateListener.setMinimizedSidebar(false);

		let regexMatch = this.router.lastRouteRegexMatches;

		if (regexMatch && regexMatch[1]) {
			this.establishmentUid = UuidUtils.scopedToVisual<UuidScopeEstablishment>(regexMatch[1] as ScopedUuid<UuidScopeEstablishment>, uuidScopeEstablishment);
		}

		let savedPreferences = this.tableColumnsRepository.getColumnPreferences(this.tableKey, this.tableColumns);
		if (savedPreferences) this.tableColumns = savedPreferences;

		this.savedFilters = this.savedFiltersRepository.getFilters(this.tableKey) ?? [];

		// Check permissions for form components and disable actions accordingly
		if (!ComponentUtils.hasPermissions(WalletDeclareStateFormComponentHasRequiredPermissions)) {
			this.disabledActions.push('lost');
			this.disabledActions.push('stolen');
		}

		if (!ComponentUtils.hasPermissions(WalletNetworkTransactionFormComponentHasRequiredPermissions)) {
			this.disabledActions.push('empty');
			this.disabledActions.push('enable');
		}
	}

	async mounted() {
		// Check for essential page functionality
		if(!ComponentUtils.hasPermissions((ownedPermissions: ApplicationPermission[]) => {
			return EstablishmentAccountPermissionModel.hasPermissionsForContracts(ownedPermissions, [
				CashlessHttpWalletContract.list,
				CashlessHttpCurrencyContract.list,
			])
		})) {
			this.forbidden = true;
			this.loading = false;
			return;
		}

		this.currencies = (await this.currencyRepository.callContract('list', {establishmentUid: this.establishmentUid}, undefined)).success();
		this.filterParameters['currency'].dropdownValues = this.currencies.map((currency) => {
			return {
				name: currency.name,
				value: currency.id
			}
		});

		await this.searchWallets({});

		this.loading = false;
	}

	async toggleWallet(wallet: WalletApiOut) {
		if (this.selectedWallet && this.selectedWallet.chipId === wallet.chipId) {
			this.selectedWallet = null;
		} else {
			this.selectedWallet = wallet;
			this.selectedWalletCustomerChip = null;

			const transactionChips = (await this.customerChipRepository.callContract('search', {establishmentUid: this.establishmentUid}, {
				filter: {
					group: QueryFilterGroupClause.AND,
					filters: [{
						name: 'publicChipId',
						value: this.selectedWallet.chipVisualId,
						operator: QueryOperator.EQUAL
					}, {
						name: 'startingDatetime',
						value: new Date().toISOString(),
						operator: QueryOperator.LESS_OR_EQUAL
					}, {
						group: QueryFilterGroupClause.OR,
						filters: [{
							name: 'endingDatetime',
							value: null,
							operator: QueryOperator.EQUAL
						}, {
							name: 'endingDatetime',
							value: new Date().toISOString(),
							operator: QueryOperator.MORE_OR_EQUAL
						}]
					}]
				}
			})).success();

			this.selectedWalletCustomerChip = transactionChips[0] ?? null;
		}
	}

	saveColumnPreferences(columns: TableColumn[]) {
		this.tableColumnsRepository.saveColumnsPreferences(this.tableKey, columns);
	}

	async sorted(data: {
		name: string,
		direction: 'asc' | 'desc'
	}) {
		this.loading = true;
		this.filters.sorts = [{
			name: data.name,
			order: data.direction === 'asc' ? 'ASC' : 'DESC'
		}];
		this.wallets = (await this.walletsRepository.callContract('search', {establishmentUid: this.establishmentUid}, this.filters)).success();

		this.loading = false;
	}


	async searchWallets(filters: TypedQuerySearch<typeof CashlessHttpWalletContractSearchConfig>, cursor: {after?: Uuid, before?: Uuid}|null = null) {
		this.loading = true;
		this.filters = {...filters};

		if(!cursor) {
			const data = (await this.walletsRepository.callContract('searchCount', {establishmentUid: this.establishmentUid}, filters)).success();
			this.pagination.totalResults = data.rows;
			this.pagination.currentPage = 1;
			this.pagination.estimateTotal = data.estimate;
		}

		filters.elementsPerPage = this.pagination.resultsPerPage;
		if(cursor && cursor.after) filters.cursorAfter = cursor.after;
		if(cursor && cursor.before) filters.cursorBefore = cursor.before;
		this.wallets = (await this.walletsRepository.callContract('search', {establishmentUid: this.establishmentUid}, filters)).success();
		this.appliedFilters = filters;
		this.loading = false;
	}


	async search(search: string) {
		const filters: (QueryAllowedFiltersToTypedQueryFilters<typeof CashlessHttpWalletContractSearchConfig.filters>|TypedQueryFilterGroup<typeof CashlessHttpWalletContractSearchConfig.filters>)[] = [];
		if (search.length === 8 && !search.includes('-')) {
			filters.push({
				name: 'chipVisualId',
				value: search,
				operator: QueryOperator.EQUAL
			});
		}
		const split = search.split('-');
		if (split.length === 2 && split[0].length === 4 && split[1].length === 4) {
			filters.push({
				name: 'chipVisualId',
				value: split.join(''),
				operator: QueryOperator.EQUAL
			});
		}

		if (parseInt(search, 10) && !search.match(/[a-zA-Z]/)) {
			const searchedPrice = parseFloat(search.replace(',', '.'));
			let searchPriceCeil: number | null = null;
			const modulus = searchedPrice % 1;
			if (modulus === 0) {
				searchPriceCeil = searchedPrice + 1;
			} else {
				const decimalNumber = (searchedPrice + '').split('.')[1].length;
				searchPriceCeil = searchedPrice + (1 / Math.pow(10, decimalNumber));
			}

			filters.push({
				group: QueryFilterGroupClause.AND,
				filters: [{
					name: 'balance',
					value: Math.ceil(searchedPrice * 100),
					operator: QueryOperator.MORE_OR_EQUAL
				}, {
					name: 'balance',
					value: Math.ceil(searchPriceCeil * 100),
					operator: QueryOperator.LESS
				}]
			});
		}

		if (filters.length === 0) {
			await this.searchWallets({});
		} else if (filters.length === 1) {
			await this.searchWallets({
				filter: filters[0] as any
			});
		} else {
			await this.searchWallets({
				filter: {
					group: QueryFilterGroupClause.OR,
					filters: filters as any
				}
			});
		}
	}

	nextPage() {
		this.pagination.currentPage++;
		this.searchWallets(this.filters, {after: this.wallets[this.wallets.length - 1].uid})
	}

	previousPage() {
		this.pagination.currentPage--;
		this.searchWallets(this.filters, {before: this.wallets[0].uid})
	}

	saveFilter(name: string) {
		if(this.filters.filter) {
			this.savedFilters.push({
				name: name,
				filter: this.filters.filter
			});
			this.savedFiltersRepository.saveFilters(this.tableKey, this.savedFilters);
		}
	}

	selectFilter(savedFilter: SavedFilter) {
		this.filters.filter = savedFilter.filter as any;
		this.searchWallets(this.filters);
	}

	deleteFilter(index: number) {
		this.savedFilters.splice(index, 1);
		this.savedFiltersRepository.saveFilters(this.tableKey, this.savedFilters);
	}

	getCorrespondingCurrency(currencyId: number) {
		const currency = this.currencies.find((currency) => currency.id === currencyId);
		if (!currency) throw new Error(`missing_currency`);
		return currency;
	}

	getCustomerUrl(customerUid: VisualScopedUuid<UuidScopeCustomer_customer>) {
		return EstablishmentUrlBuilder.buildUrl('/customers?uid=' + UuidUtils.visualToUuid(customerUid));
	}

	async loadChipHistory(wallet: WalletApiOut) {
		this.loadingCustomerChipHistory = true;

		let filter: any;

		if (wallet.chipId !== null) {
			filter = {
				name: 'chipId',
				value: wallet.chipId,
				operator: QueryOperator.EQUAL
			};
		} else if (wallet.chipUid !== null) {
			filter = {
				name: 'chipUid',
				value: wallet.chipUid,
				operator: QueryOperator.EQUAL
			};
		} else {
			filter = {
				name: 'publicChipId',
				value: wallet.chipVisualId,
				operator: QueryOperator.EQUAL
			};
		}

		this.customerChipHistory[wallet.uid] = (await this.customerChipRepository.callContract('search', {establishmentUid: this.establishmentUid}, {
			filter: filter
		})).success();
		this.loadingCustomerChipHistory = false;
	}

	goToChipTransactions(publicChipId: string) {
		window.open(EstablishmentUrlBuilder.buildUrl('/transactions?chip=' + publicChipId), '_blank');
	}

	get chipDropdownActions() {
		const values: DropdownButtonAction[] = [{
			id: 'lost',
			name: 'Déclarer perdue',
			icon: 'fa-regular fa-location-exclamation'
		}, {
			id: 'stolen',
			name: 'Déclarer volée',
			icon: 'fa-regular fa-location-exclamation'
		}, {
			id: 'enable',
			name: 'Activer',
			icon: 'fa-regular fa-circle-check'
		}, {
			id: 'empty',
			name: 'Vider le solde',
			icon: 'fa-regular fa-trash-alt'
		}];

		return values.filter((value) => !this.disabledActions.includes(value.id));
	}

	async chipDropdownClicked(action: DropdownButtonAction, wallet: WalletApiOut) {
		if(this.disabledActions.includes(action.id)) return;

		if (action.id === 'empty') {
			this.showEmptyChipConfirmationModal = {
				type: 'CLEAR',
				wallet: wallet
			}
		} else if (action.id === 'enable') {
			this.showEmptyChipConfirmationModal = {
				type: 'ENABLE',
				wallet: wallet
			}
		} else if (action.id === 'lost') {
			this.showUnusableChipConfirmationModal = {
				reason: WalletInactiveReason.LOST,
				wallet: wallet
			}
		} else if (action.id === 'stolen') {
			this.showUnusableChipConfirmationModal = {
				reason: WalletInactiveReason.STOLEN,
				wallet: wallet
			}
		}
	}

	toggleExportModal() {
		console.log(77)
		this.showExportModal = true;
	}

	processExport(data: ChosenExport) {
		if(data.exportId === 'raw') {
			this.exportWallets(data.format, data.exportZeroLines);
		}
	}


	async exportWallets(type: ExportFileType, exportZeroLines: boolean) {
		this.downloadingExport = true;

		this.$nextTick(() => {
			const jsonData: Record<string, string>[] = this.wallets.map((wallet) => {
				return {
					'ID':wallet.chipVisualId,
					'Balance': MoneyFilter(wallet.balance,null)+"",
					'Date de création': DateUtils.formatDateFrFormat(wallet.creationDatetimeOnServer)+"",
					'Heure de création': DateUtils.formatDateFrFormat(wallet.creationDatetimeOnServer)+""
				};
			})

			MultiFormatExporter.downloadData([
				{name: 'ID', type: 'AUTO'},
				{name: 'Balance', type: 'MONEY'},
				{name: 'Date de création', type: 'AUTO'},
				{name: 'Heure de création', type: 'AUTO'},
			], jsonData, type);

			this.downloadingExport = false;
		});
	}
}
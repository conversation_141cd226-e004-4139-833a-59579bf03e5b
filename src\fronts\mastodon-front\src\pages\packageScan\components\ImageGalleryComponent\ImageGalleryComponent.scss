.image-gallery-component {
    position: fixed;
    inset: 0;
    z-index: 1040;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;


    .images {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: flex-start;
        gap: 20px;
        padding: 40px 40px 200px 40px;
        box-sizing: border-box;
        overflow: scroll;
        height: 100%;

        img {
            width: 100%;
        }
    }

    .close-button {
        position: absolute;
        bottom: 40px;
        left: 50%;
        transform: translateX(-50%);
        font-size: 20px;
        border-radius: 50%;
        cursor: pointer;
        background: white;
        height: 60px;
        width: 60px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
}
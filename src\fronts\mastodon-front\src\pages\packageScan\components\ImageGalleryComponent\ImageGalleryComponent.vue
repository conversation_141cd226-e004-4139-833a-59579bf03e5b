<script lang="ts" src="./ImageGalleryComponent.ts"/>

<style lang="sass" scoped>
@import './ImageGalleryComponent.scss'
</style>

<template>
    <div class="image-gallery-component">
        <div class="images">
            <img v-for="image of images" :src="image.url" />
        </div>

        <div class="close-button">
            <i class="fa-regular fa-xmark" @click="close()"></i>
        </div>
    </div>
</template>
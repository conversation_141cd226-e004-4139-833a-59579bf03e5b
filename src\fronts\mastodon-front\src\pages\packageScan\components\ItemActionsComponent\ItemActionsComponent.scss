.item-actions-component {
    position: fixed;
    inset: 0;
    z-index: 1020;
    background: rgba(0, 0, 0, 0.2);
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    justify-content: center;
    gap: 20px;
    padding: 40px;
    opacity: 0;
    transition: opacity .3s cubic-bezier(0.22, 1, 0.36, 1);

    .actions {
        display: flex;
        flex-direction: column;
        gap: 20px;
    }

    .action-group {
        transform: translateY(200%);
        opacity: 0;
        transition: transform .3s cubic-bezier(0.22, 1, 0.36, 1), opacity .3s cubic-bezier(0.22, 1, 0.36, 1);

        display: flex;
        flex-direction: column;
        align-items: flex-end;
        justify-content: center;
        gap: 4px;

        .action {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            background: white;
            border-radius: 18px;
            cursor: pointer;
            width: 60px;
            aspect-ratio: 1/1;

            i {
                font-size: 22px;
            }
        }

        .text {
            font-size: 13px;
            font-weight: 500;
            background: white;
            padding: 5px 10px;
            border-radius: 6px;
        }
    }

    &.opened {
        opacity: 1;

        .action-group {
            transform: none;
            opacity: 1;
        }
    }

}
import {Component, Prop, Vue} from "vue-facing-decorator";
import {AutoWired} from "@groupk/horizon2-core";
import {BarcodeScannerManager} from "../../BarcodeScannerManager";
import {BaserowObjectRepository} from "../../../../../../../shared/repositories/BaserowObjectRepository";
import {ObjectApiIn, ObjectApiOut} from "../../../../../../../shared/baserowContracts/Objects/ObjectsApiClasses";

@Component({
    components: {},
    emits: ['close', 'created']
})
export default class ObjectFormComponent extends Vue {
    @Prop({required: true}) editingObject!: ObjectApiOut;

    comment: string = '';
    creating: boolean = false;

    @AutoWired(BaserowObjectRepository) accessor baserowObjectRepository!: BaserowObjectRepository;
    @AutoWired(BarcodeScannerManager) accessor barcodeScannerManager!: BarcodeScannerManager;

    mounted() {
        this.comment = this.editingObject.comment ?? '';
        this.barcodeScannerManager.customCallback = null;
    }

    async save() {
        this.creating = true;

        const response = await this.baserowObjectRepository.callContract('update', {objectId: this.editingObject.id}, new ObjectApiIn({
            comment: this.comment,
            code: this.editingObject.code,
            packageId: undefined,
            returned: this.editingObject.returned,
            images: undefined,
            returnImages: undefined
        }));


        if(!response.isSuccess()) {
        } else {
            this.$emit('updated', response.success());
        }

        this.creating = false;
        this.close();
    }

    close() {
        this.$emit('close');
    }
}
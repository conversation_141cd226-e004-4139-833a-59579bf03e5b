<script lang="ts" src="./ObjectFormComponent.ts"/>

<style lang="sass" scoped>
@import './ObjectFormComponent.scss'
</style>

<template>
    <div class="package-form-component">
        <div class="container">
            <div class="actions">
                <div class="action-group" @click="close()">
                    <div class="action">
                        <i class="fa-regular fa-arrow-left"></i>
                    </div>
                    <span class="text"> retour </span>
                </div>
            </div>

            <div class="form">
                <div class="input-group">
                    <input v-model="comment" type="text" placeholder="Commentaire" />
                </div>

                <div class="buttons">
                    <button class="button" @click="save()" :class="{loading: creating, disabled: creating}">
                        Sauvegarder
                    </button>
                </div>
            </div>
        </div>
    </div>
</template>
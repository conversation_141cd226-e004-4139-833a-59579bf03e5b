<script lang="ts" src="./PackageComponent.ts"/>

<style lang="sass" scoped>
@import './PackageComponent.scss'
</style>

<template>
    <div class="package-component">
        <div class="loading-container" v-if="loading">
            <div class="loader"></div>
        </div>
        <div v-else class="container">
            <div class="actions">
                <div class="action-group" @click="close()">
                    <div class="action">
                        <i class="fa-regular fa-arrow-left"></i>
                    </div>
                    <span class="text"> retour </span>
                </div>

                <div class="action-group" @click="showValidation = true;">
                    <div class="action">
                        <i class="fa-regular fa-question-circle"></i>
                    </div>
                    <span class="text"> valider le colis </span>
                </div>

                <div class="action-group" @click="showForm = true;">
                    <div class="action">
                        <i class="fa-regular fa-pen-line"></i>
                    </div>
                    <span class="text"> modifier </span>
                </div>
            </div>

            <h3> {{ packageData.name }} - Objets ({{ objectsInPackage.length }})</h3>

            <div class="total-per-name-recap">
                <div class="total-per-name" v-for="(count, objectName) of totalPerName">
                    <span class="name"> {{ objectName }} </span>
                    <span> x{{ count }} </span>
                </div>
            </div>

            <div class="objects">
                <div class="object" v-for="objectData of objectsInPackage" @click="showActionsForObject = objectData">
                    <div class="top">
                        <span class="name"> {{ getObjectTranslation(objectData.code) }} </span>

                        <div class="images">
                            <i class="fa-regular fa-image" v-if="objectData.images.length > 0"></i>
                            <i class="fa-regular fa-image" v-if="objectData.returnImages.length > 0"></i>
                        </div>

                        <i class="fa-regular fa-ellipsis-vertical"></i>
                    </div>
                    <div class="bottom" v-if="objectData.comment">
                        {{ objectData.comment }}
                    </div>
                </div>
            </div>
        </div>

        <div class="scanning-modal already-in" v-if="alreadyIn">
            <div class="container">
                <div class="title"> Ce code est déjà présent dans le colis </div>
                <div class="subtitle"> {{ getObjectTranslation(alreadyIn.code) }} </div>
                <div class="buttons">
                    <button class="red button" :class="{loading: deleting, disabled: deleting}" @click="deleteObjectFromPackage(alreadyIn.id)">
                        Supprimer du colis
                    </button>
                    <button class="grey button" @click="showActionsForObject = alreadyIn; alreadyIn = null;">
                        Options
                    </button>
                    <button class="grey button" @click="alreadyIn = null;">
                        Continuer
                    </button>
                </div>
            </div>
        </div>

        <div class="scanning-modal" v-else-if="scanning">
            <div class="container">
                <div class="loading-container">
                    <div class="loader"></div>
                </div>
            </div>
        </div>

        <package-validation
            v-if="showValidation"
            :package-data="packageData"
            :objects-in-package="objectsInPackage"
            :object-translations="objectTranslations"
            @updated="updatePackage($event, true)"
            @updated-object="updateObject($event)"
            @close="showValidation = false; setupListener()"
        ></package-validation>

        <package-form
            v-if="showForm"
            :editing-package="packageData"
            @updated="updatePackage($event)"
            @close="showForm = false; setupListener()"
        ></package-form>

        <object-form
            v-if="showObjectForm"
            :editing-object="showObjectForm"
            @updated="updateObject($event)"
            @close="showObjectForm = null; setupListener()"
        ></object-form>

        <item-actions
            v-if="showActionsForObject"
            :actions="getObjectActions(showActionsForObject)"
            @action-clicked="actionClicked($event)"
            @close="showActionsForObject = null"
        ></item-actions>

        <camera
            v-if="showCamera && showCamera.opened"
            @photo-captured="photoCaptured($event)"
            @close="showCamera.opened = false"
        ></camera>

        <image-gallery
            v-if="showObjectImage || showObjectReturnImage"
            :images="showObjectImage ? showObjectImage.images : (showObjectReturnImage ? showObjectReturnImage.returnImages : [])"
            @close="showObjectImage = null; showObjectReturnImage = null"
        ></image-gallery>
    </div>
</template>
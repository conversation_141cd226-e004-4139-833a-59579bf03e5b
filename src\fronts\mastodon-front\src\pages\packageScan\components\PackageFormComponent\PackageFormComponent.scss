.package-form-component {
    position: fixed;
    inset: 0;
    z-index: 1000;
    height: 100%;
    background: #f0f2f3;
    overflow: auto;

    .container {
        display: flex;
        flex-direction: column;
        gap: 20px;
        padding: 20px;
        max-width: 700px;
        margin: auto;

        h3 {
            font-size: 18px;
            font-weight: 600;
            margin: 10px 0 0 0;
        }

        .actions {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            grid-gap: 20px;

            .action-group {
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                gap: 4px;

                .action {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    background: white;
                    border-radius: 20px;
                    cursor: pointer;
                    width: 80%;
                    aspect-ratio: 1/1;

                    i {
                        font-size: 22px;
                    }
                }

                .text {
                    font-size: 14px;
                }
            }
        }

        .input-group {
            input {
                font-size: 16px;
                padding: 20px 15px;
                border: none;
                background: white;
            }
        }

        .buttons {
            margin: 0;

            .button {
                width: 100%;
                padding: 20px;
                font-size: 18px;
                font-weight: 600;
            }
        }
    }
}
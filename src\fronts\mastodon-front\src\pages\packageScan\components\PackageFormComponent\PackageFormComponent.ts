import {Component, Prop, Vue} from "vue-facing-decorator";
import {AutoWired, FetchError, genericFetch} from "@groupk/horizon2-core";
import {BarcodeScannerManager} from "../../BarcodeScannerManager";
import {BaserowPackageRepository} from "../../../../../../../shared/repositories/BaserowPackageRepository";
import {PackageApiIn, PackageApiOut} from "../../../../../../../shared/baserowContracts/Package/PackageApiClasses";

@Component({
    components: {},
    emits: ['close', 'created']
})
export default class PackageFormComponent extends Vue {
    @Prop({default: null}) editingPackage!: PackageApiOut|null;

    name: string = '';
    comment: string = '';
    shippingBarcode: string = '';
    returnBarcode: string = '';

    creating: boolean = false;

    @AutoWired(BaserowPackageRepository) accessor baserowPackageRepository!: BaserowPackageRepository;
    @AutoWired(BarcodeScannerManager) accessor barcodeScannerManager!: BarcodeScannerManager;

    mounted() {
        if(this.editingPackage) {
            this.name = this.editingPackage.name;
            this.comment = this.editingPackage.comment ?? '';
            this.shippingBarcode = this.editingPackage.shipping;
            this.returnBarcode = this.editingPackage.returnCode;
        }

        this.barcodeScannerManager.customCallback = null;
    }

    save() {
        if(this.editingPackage) {
            this.update();
        } else {
            this.create();
        }
    }

    async create() {
        this.creating = true;

        const response = await this.baserowPackageRepository.callContract('create', undefined, new PackageApiIn({
            name: this.name,
            comment: this.comment,
            shipping: this.shippingBarcode,
            returnCode: this.returnBarcode,
            validated: false
        }));

        if(!response.isSuccess()) {
        } else {
            this.$emit('created', response.success());
        }

        this.creating = false;
        this.close();
    }

    async update() {
        if(!this.editingPackage) return;
        this.creating = true;

        const response = await this.baserowPackageRepository.callContract('update', {packageId: this.editingPackage.id}, new PackageApiIn({
            name: this.name,
            comment: this.comment,
            shipping: this.shippingBarcode,
            returnCode: this.returnBarcode,
            validated: false
        }));

        if(!response.isSuccess()) {
        } else {
            this.$emit('updated', response.success());
        }

        this.creating = false;
        this.close();
    }

    close() {
        this.$emit('close');
    }
}
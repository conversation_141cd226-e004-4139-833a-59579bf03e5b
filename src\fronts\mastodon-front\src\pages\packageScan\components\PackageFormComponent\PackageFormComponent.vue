<script lang="ts" src="./PackageFormComponent.ts"/>

<style lang="sass" scoped>
@import './PackageFormComponent.scss'
</style>

<template>
    <div class="package-form-component">
        <div class="container">
            <div class="actions">
                <div class="action-group" @click="close()">
                    <div class="action">
                        <i class="fa-regular fa-arrow-left"></i>
                    </div>
                    <span class="text"> retour </span>
                </div>
            </div>

            <div class="form">
                <div class="input-group">
                    <input v-model="name" type="text" placeholder="Nom du colis" />
                </div>

                <div class="input-group">
                    <input v-model="comment" type="text" placeholder="Commentaire" />
                </div>

                <div class="input-group">
                    <input v-model="shippingBarcode" type="text" placeholder="Code barre d'envoi" />
                </div>

                <div class="input-group">
                    <input v-model="returnBarcode" type="text" placeholder="Code barre de retour" />
                </div>

                <div class="buttons">
                    <button class="button" @click="save()" :class="{loading: creating, disabled: creating}">
                        <template v-if="!editingPackage"> Créer le colis </template>
                        <template v-else> Sauvegarder </template>
                    </button>
                </div>
            </div>
        </div>
    </div>
</template>
import {Component, Prop, Vue} from "vue-facing-decorator";
import {AutoWired, FetchError, genericFetch} from "@groupk/horizon2-core";
import {BarcodeExternalNative_QRCodeReaderReturn, VibratorNative} from "@groupk/native-bridge";
import {BarcodeScannerManager} from "../../BarcodeScannerManager";
import CameraComponent from "../CameraComponent/CameraComponent.vue";
import ItemActionsComponent from "../ItemActionsComponent/ItemActionsComponent.vue";
import {ItemAction} from "../ItemActionsComponent/ItemActionsComponent";
import ImageGalleryComponent from "../ImageGalleryComponent/ImageGalleryComponent.vue";
import ObjectFormComponent from "../ObjectFormComponent/ObjectFormComponent.vue";
import {ObjectApiIn, ObjectApiOut} from "../../../../../../../shared/baserowContracts/Objects/ObjectsApiClasses";
import {BaserowObjectRepository} from "../../../../../../../shared/repositories/BaserowObjectRepository";
import {PackageApiIn, PackageApiOut} from "../../../../../../../shared/baserowContracts/Package/PackageApiClasses";
import {BaserowPackageRepository} from "../../../../../../../shared/repositories/BaserowPackageRepository";
import {ObjectNamesApiOut} from "../../../../../../../shared/baserowContracts/ObjectNames/ObjectNamesApiClasses";

@Component({
    components: {
        'camera': CameraComponent,
        'item-actions': ItemActionsComponent,
        'image-gallery': ImageGalleryComponent,
        'object-form': ObjectFormComponent
    },
    emits: ['close', 'updated', 'updated-object']
})
export default class PackageValidationComponent extends Vue {
    @Prop({required: true}) packageData!: PackageApiOut;
    @Prop({required: true}) objectsInPackage!: ObjectApiOut[];
    @Prop({required: true}) objectTranslations!: ObjectNamesApiOut[];

    notIn: ObjectApiOut|null = null;
    scanState: 'ERROR'|'SUCCESS'|null = null;

    showActionsForObject: ObjectApiOut|null = null;
    showCamera: { opened: boolean, forObject: ObjectApiOut }|null = null;
    showObjectImage: ObjectApiOut|null = null;
    showObjectForm: ObjectApiOut|null = null;
    showObjectReturnImage: ObjectApiOut|null = null;

    scanning: boolean = false;
    saving: boolean = false;

    @AutoWired(BaserowObjectRepository) accessor baserowObjectRepository!: BaserowObjectRepository;
    @AutoWired(BaserowPackageRepository) accessor baserowPackageRepository!: BaserowPackageRepository;
    @AutoWired(BarcodeScannerManager) accessor barcodeScannerManager!: BarcodeScannerManager;
    @AutoWired(VibratorNative) accessor vibratorNative!: VibratorNative;

    async mounted() {
        this.setupListener();
    }

    setupListener() {
        this.barcodeScannerManager.customCallback = this.barCodeRead;
    }

    get notScannedObjects() {
        return this.objectsInPackage.filter((object) => !object.returned);
    }

    get scannedObjects() {
        return this.objectsInPackage.filter((object) => object.returned);
    }

    async barCodeRead(data: BarcodeExternalNative_QRCodeReaderReturn) {
        if(this.scanning) return;
        this.scanning = true;

        const correspondingAirtableObject = this.isIn(data.content);
        if(correspondingAirtableObject === null) {
            this.notIn = correspondingAirtableObject; // Not used for now
            this.scanState = 'ERROR';
        }
        else {
            if(correspondingAirtableObject.returned) {
                this.scanState = 'SUCCESS';
            } else {
                const response = await this.validateObject(correspondingAirtableObject);

                if(response instanceof FetchError) {
                    this.scanState = 'ERROR';
                } else {
                    this.scanState = 'SUCCESS';
                }
            }
        }

        setTimeout(() => this.scanState = null, 200);

        this.scanning = false;
    }

    isIn(objectId: string): ObjectApiOut|null {
        return this.objectsInPackage.find((object) => object.code === objectId) ?? null;
    }

    async validateObject(object: ObjectApiOut, validate: boolean = true) {
        const response = await this.baserowObjectRepository.callContract('update', {objectId: object.id}, new ObjectApiIn({
            code: object.code,
            packageId: undefined,
            returned: validate,
            images: undefined,
            returnImages: undefined,
            comment: object.comment ?? ''
        }));

        if(!response.isSuccess()) {
        } else {
            this.updateObject(response.success());
        }

        return response;
    }

    async validatePackage() {
        this.saving = true;

        const response = await this.baserowPackageRepository.callContract('update', {packageId: this.packageData.id}, new PackageApiIn({
            name: this.packageData.name,
            shipping: this.packageData.shipping,
            returnCode: this.packageData.returnCode,
            validated: true,
            comment: this.packageData.comment ?? ''
        }));

        if(!response.isSuccess()) {
        } else {
        }

        this.saving = false;
        this.close();
    }

    getObjectTranslation(objectId: string) {
        const translationObject = this.objectTranslations.find((object) => object.code === objectId);
        if(translationObject) {
            if(translationObject.brand) {
                return translationObject.brand + ' ' + translationObject.name + ' - ' + objectId;
            } else {
                return translationObject.name + ' - ' + objectId;
            }
        } else {
            return objectId;
        }
    }

    async actionClicked(action: ItemAction) {
        if(!this.showActionsForObject) return;
        if(action.id === 'take-picture') {
            this.showCamera = {opened: true, forObject: this.showActionsForObject};
        } else if(action.id === 'show-return-image') {
            this.showObjectReturnImage = this.showActionsForObject;
        } else if(action.id === 'show-image') {
            this.showObjectImage = this.showActionsForObject;
        } else if(action.id === 'validate') {
            this.scanning = true;
            await this.validateObject(this.showActionsForObject);
            this.scanning = false;
        } else if(action.id === 'unvalidate') {
            this.scanning = true;
            await this.validateObject(this.showActionsForObject, false);
            this.scanning = false;
        }else if(action.id === 'edit') {
            this.showObjectForm = this.showActionsForObject;
        }
    }

    getObjectActions(objectData: ObjectApiOut) {
        const actions: ItemAction[] = [
            {id: 'take-picture', icon: 'fa-regular fa-camera', text: 'Image de retour'},
            {id: 'edit', icon: 'fa-regular fa-pen-line', text: 'Modifier'},
        ];

        if(objectData.returnImages) {
            actions.unshift({id: 'show-return-image', icon: 'fa-regular fa-image', text: 'Visualiser les images de retour'});
        }

        if(objectData.images) {
            actions.unshift({id: 'show-image', icon: 'fa-regular fa-image', text: 'Visualiser les images'});
        }

        if(objectData.returned) {
            actions.unshift({id: 'unvalidate', icon: 'fa-regular fa-check', text: 'Dé-valider l\'objet'});
        } else {
            actions.unshift({id: 'validate', icon: 'fa-regular fa-check', text: 'Valider l\'objet'});
        }

        return actions;
    }


    async photoCaptured(dataUrl: string) {
        if(!this.showCamera) return;
        this.scanning = true;

        // Convert dataUrl to File object
        const response = await fetch(dataUrl);
        const blob = await response.blob();
        const file = new File([blob], 'captured-image.jpg', { type: blob.type });

        const formData = new FormData()
        formData.append('file', file);

        const responseFile = await genericFetch({
            method: "POST",
            url: "https://baserow-api.lab.weecop.fr/api/user-files/upload-file/",
            headers: {
                Authorization: "Token fCK4Uh9UE8bo8KKRM8dHPvReYiQuf7wL",
            },
            body: formData
        });

        if(responseFile instanceof FetchError) {
        } else {
            const data = await responseFile.json();
            const responseObject = await this.baserowObjectRepository.callContract('update', {objectId: this.showCamera.forObject.id}, new ObjectApiIn({
                code: this.showCamera.forObject.code,
                packageId: undefined,
                returned: this.showCamera.forObject.returned,
                images: undefined,
                returnImages: [data.name].concat(this.showCamera.forObject.returnImages.map((image) => image.name)),
                comment: this.showCamera.forObject.comment ?? ''
            }));

            if(responseObject.isSuccess()) {
                this.updateObject(responseObject.success());
            }
        }

        this.showCamera = null;
        this.scanning = false;
    }


    get totalPerName() {
        const totalPerName: Record<string, number> = {};
        for(let object of this.notScannedObjects) {
            const translationObject = this.objectTranslations.find((translation) => translation.code === object.code);
            if(!translationObject) {
                if(!totalPerName['Inconnu']) {
                    totalPerName['Inconnu'] = 0;
                }
                totalPerName['Inconnu']++;
            } else {
                if(!totalPerName[translationObject.name]) {
                    totalPerName[translationObject.name] = 0;
                }
                totalPerName[translationObject.name]++;
            }

        }
        return totalPerName;
    }


    updateObject(objectData: ObjectApiOut) {
        this.$emit('updated-object', objectData);
    }

    close() {
        this.$emit('close');
    }
}
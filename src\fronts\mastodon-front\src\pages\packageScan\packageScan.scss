#package-scan-page {
    height: 100%;
    background: #f0f2f3;
    overflow: auto;

    .loading-container {
        margin: 0;
        height: 100%;
    }

    .container {
        display: flex;
        flex-direction: column;
        gap: 20px;
        padding: 20px;
        max-width: 700px;
        margin: auto;

        h3 {
            font-size: 18px;
            font-weight: 600;
            margin: 10px 0 0 0;
        }

        .actions {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            grid-gap: 20px;

            .action-group {
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                gap: 4px;

                .action {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    background: white;
                    border-radius: 20px;
                    cursor: pointer;
                    width: 80%;
                    aspect-ratio: 1/1;

                    i {
                        font-size: 22px;
                    }
                }

                .text {
                    font-size: 14px;
                }
            }
        }

        .packages {
            display: flex;
            flex-direction: column;
            gap: 10px;

            .package {
                display: flex;
                flex-direction: column;
                gap: 6px;
                background: white;
                padding: 20px;
                border-radius: 20px;

                .top {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                }

                .name {
                    font-size: 18px;
                    font-weight: 500;
                }

                i {
                    font-size: 18px;
                }

                .bottom {
                    display: flex;
                    align-items: center;
                    gap: 10px;

                    .subtitle {
                        display: flex;
                        align-items: center;
                        gap: 6px;
                        font-size: 15px;
                        color: grey;

                        i {
                            font-size: 13px;
                        }
                    }

                    .separator {
                        width: 5px;
                        height: 5px;
                        background: lightgrey;
                        border-radius: 50%;
                    }
                }

                &.done {
                    background: rgba(0, 128, 0, 0.6);
                    color: white;

                    .subtitle {
                        font-size: 15px;
                        color: whitesmoke;
                    }
                }
            }
        }
    }
}
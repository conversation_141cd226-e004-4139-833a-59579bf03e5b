import {Component, Vue} from "vue-facing-decorator";
import {AutoWired, genericFetch} from "@groupk/horizon2-core";
import SidebarStateListener from "../../../../../shared/utils/SidebarStateListener";
import PackageComponent from "./components/PackageComponent/PackageComponent.vue";
import PackageFormComponent from "./components/PackageFormComponent/PackageFormComponent.vue";
import {BarcodeExternalNative, BarcodeExternalNative_QRCodeReaderReturn, SystemInfoNative} from "@groupk/native-bridge";
import {BarcodeScannerManager} from "./BarcodeScannerManager";
import {EventListenerWrapper} from "@groupk/horizon2-core";
import {BaserowPackageRepository} from "../../../../../shared/repositories/BaserowPackageRepository";
import {BaserowObjectNamesRepository} from "../../../../../shared/repositories/BaserowObjectNamesRepository";
import {ObjectNamesApiOut} from "../../../../../shared/baserowContracts/ObjectNames/ObjectNamesApiClasses";
import {PackageApiOut} from "../../../../../shared/baserowContracts/Package/PackageApiClasses";

@Component({
    components: {
        'package-form': PackageFormComponent,
        'package': PackageComponent
    }
})
export default class packageScan extends Vue {
    existingPackages: PackageApiOut[] = [];
    objectTranslations: ObjectNamesApiOut[] = [];

    selectedPackage: PackageApiOut|null = null;
    listenerToUnload!: EventListenerWrapper;

    showForm: boolean = false;
    loading: boolean = true;

    @AutoWired(SidebarStateListener) accessor sidebarStateListener!: SidebarStateListener;
    @AutoWired(BarcodeExternalNative) accessor barcodeExternalNative!: BarcodeExternalNative;
    @AutoWired(BarcodeScannerManager) accessor barcodeScannerManager!: BarcodeScannerManager;
    @AutoWired(BaserowObjectNamesRepository) accessor baserowObjectNamesRepository!: BaserowObjectNamesRepository;
    @AutoWired(BaserowPackageRepository) accessor baserowPackageRepository!: BaserowPackageRepository;
    @AutoWired(SystemInfoNative) accessor systemInfoNative!: SystemInfoNative;

    beforeMount() {
        this.sidebarStateListener.setHiddenSidebar(true);
        this.sidebarStateListener.setHiddenTopBar(true);

        this.listenerToUnload = this.barcodeExternalNative.on('allRead', (data) => this.barcodeScannerManager.callback(data));
        this.systemInfoNative.disableBackButton().catch(() => {});

        this.setupListener();
    }

    async mounted() {
        await this.loadTranslations();
        await this.loadPackages()

        this.loading = false;
    }


    unmounted() {
        this.barcodeExternalNative.off('allRead', this.listenerToUnload.listener);
    }

    setupListener() {
        this.barcodeScannerManager.customCallback = this.barCodeRead;
    }

    async loadPackages() {
        this.loading = true;

        const response = await this.baserowPackageRepository.callContract('list', undefined, undefined);
        if(!response.isSuccess()) {
            console.log(response);
        } else {
            this.existingPackages = response.success().results;
            this.sortPackages();
        }

        this.loading = false;
    }

    async loadTranslations() {
        const response = await this.baserowObjectNamesRepository.callContract('list', undefined, undefined);

        if(!response.isSuccess()) {
            console.log(response);
        } else {
            this.objectTranslations = response.success().results;
        }
    }

    sortPackages() {
        this.existingPackages.sort((a: PackageApiOut, b: PackageApiOut) => {
            // Sort by creatingDate desc and validated asc
            if(a.validated === b.validated) {
                return new Date(b.creationDate).getTime() - new Date(a.creationDate).getTime();
            } else {
                return a.validated ? 1 : -1;
            }
        })
    }

    async barCodeRead(data: BarcodeExternalNative_QRCodeReaderReturn) {
        // Find package base on return then shipping
        const correspondingAirtablePackage = this.existingPackages.find((packageData) => packageData.returnCode === data.content || packageData.shipping === data.content);
        if(correspondingAirtablePackage) {
            this.selectedPackage = correspondingAirtablePackage;
        }
    }

    createdPackage(packageData: PackageApiOut) {
        this.existingPackages.push(packageData);

        this.sortPackages();
    }

    updatedPackage(updatedPackageData: PackageApiOut) {
        const index = this.existingPackages.findIndex((packageData) => packageData.id === updatedPackageData.id);
        if(index !== -1) this.existingPackages.splice(index, 1, updatedPackageData);

        this.sortPackages();
    }
}
<script lang="ts" src="./MobileStatisticsComponent.ts">
</script>

<style lang="sass">
@import './MobileStatisticsComponent.scss'
</style>

<template>
    <div class="mobile-statistics-component">
        <order-key-number-statistics
            v-if="!loading"
            :pos-state="posState"
            :local-orders="orders"
        ></order-key-number-statistics>

        <h3> Par moyen de paiement </h3>

        <div class="key-numbers">
            <div class="key-number" v-for="method of posState.paymentMethods">
                <span class="type"> {{ method.name }}</span>
                <span class="value">
                    <template v-if="selectedEmployee === 'global'">
                        {{ $filters.Money(ordersStatistics.perPaymentMethod[method.uid] ? ordersStatistics.perPaymentMethod[method.uid].success : 0) }}
                    </template>
                     <template v-else>
                        {{ $filters.Money(ordersStatistics.perSeller[selectedEmployee].perPaymentMethod[method.uid] ? ordersStatistics.perSeller[selectedEmployee].perPaymentMethod[method.uid].success : 0) }}
                    </template>
                </span>
            </div>
        </div>
    </div>
</template>
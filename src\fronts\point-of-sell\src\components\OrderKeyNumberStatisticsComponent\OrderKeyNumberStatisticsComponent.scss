.order-key-number-statistics {
    display: flex;
    flex-direction: column;
    gap: 20px;

    .line-message {
        display: flex;
        align-items: center;
        gap: 10px;
        background: #E8E8E8;
        font-size: 16px;
        padding: 15px;
        border-radius: 8px;
    }

    .key-numbers {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr;
        gap: 20px;

        @media (max-width: 900px) {
            grid-template-columns: 1fr 1fr;
        }

        .key-number {
            display: flex;
            flex-direction: column;
            gap: 10px;
            padding: 25px;
            border-radius: 8px;
            background: white;

            .type {
                font-size: 14px;
            }

            @media (max-width: 900px) {
                &.full {
                    grid-column: 1/3;
                }
            }

            .value {
                font-size: 24px;
                font-weight: 700;
            }

            .indicator {
                display: flex;
                align-items: center;
                gap: 4px;
                color: #079455;
                font-weight: 600;
                font-size: 14px;

                span {
                    color: #545454;
                    font-size: 12px;
                    font-weight: 500;
                }
            }
        }
    }
}
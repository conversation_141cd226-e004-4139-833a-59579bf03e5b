import {Component, Prop, Vue} from "vue-facing-decorator";
import {LocalOrder} from "../../model/LocalOrder";
import {OrderExecutorModel, OrderStatsModel} from "@groupk/mastodon-core";
import {PosState} from "../../model/PosState";
import {AutoWired} from "@groupk/horizon2-core";

@Component({
    components: {}
})
export default class OrderKeyNumberStatisticsComponent extends Vue {
    @Prop({required: true}) posState!: PosState;
    @Prop({required: true}) localOrders!: LocalOrder[];

    orders: LocalOrder[] = [];

    @AutoWired(OrderExecutorModel) accessor orderExecutorModel !: OrderExecutorModel;

    async mounted() {
        this.orders = this.localOrders
            .filter((order) => !order.transferred && !order.order.transferredTo)
            .filter((order) => {
                return this.posState.orderExecutorModel.getOrderTotals(order.order).purchases.withTaxesAfterDiscount !== 0;
            });
    }

    get ordersStatistics() {
        const statsModel = new OrderStatsModel(this.orderExecutorModel);
        return statsModel.compute(this.orders.map((localOrder) => localOrder.order));
    }

    get ordersWithGuestsStatistics() {
        const statsModel = new OrderStatsModel(this.orderExecutorModel);
        return statsModel.compute(
            this.orders
                .filter((order) => order.order.diningExtra && order.order.diningExtra.guestCount && order.order.diningExtra.guestCount > 0)
                .map((localOrder) => localOrder.order)
        );
    }

}
<script lang="ts" src="./OrderKeyNumberStatisticsComponent.ts"/>

<style lang="sass" scoped>
@import './OrderKeyNumberStatisticsComponent.scss'
</style>

<template>
    <div class="order-key-number-statistics">
        <div class="line-message">
            <i class="fa-regular fa-circle-info"></i>
            <span class="message"> Les commandes à zéro euro ne sont pas prises en compte dans les statistiques </span>
        </div>

        <div class="key-numbers">
            <div class="key-number">
                <span class="type"> CA Total </span>
                <span class="value"> {{ $filters.Money(ordersStatistics.totals.withTaxes) }} </span>
            </div>
            <div class="key-number">
                <span class="type"> Nombre de commandes </span>
                <span class="value"> {{ orders.length }} </span>
            </div>
            <div class="key-number">
                <span class="type"> Panier moyen / commande </span>
                <span class="value" v-if="orders.length > 0"> {{ $filters.Money(ordersStatistics.totals.withTaxes / orders.length) }} </span>
                <span class="value" v-else> {{ $filters.Money(0) }} </span>
            </div>

            <template v-if="posState.isDiningEnabled()">
                <div class="key-number">
                    <span class="type"> Nombre de couverts </span>
                    <span class="value"> {{ orders.reduce((acc, order) => acc + (order.diningExtra.guestCount ?? 0), 0) }} </span>
                </div>
                <div class="key-number full">
                    <span class="type"> Panier moyen / couvert </span>
                    <span class="value" v-if="orders.reduce((acc, order) => acc + (order.diningExtra.guestCount ?? 0), 0) > 0">
                            {{ $filters.Money(ordersWithGuestsStatistics.totals.withTaxes / orders.reduce((acc, order) => acc + (order.diningExtra.guestCount ?? 0), 0)) }}
                        </span>
                    <span class="value" v-else> {{ $filters.Money(0) }} </span>
                </div>
            </template>
        </div>

    </div>
</template>
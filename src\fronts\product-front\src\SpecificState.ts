import {AppApiOut_type, AppType} from "@groupk/mastodon-core";
import {AutoWired} from "@groupk/horizon2-core";
import {AppRepository} from "../../../shared/repositories/AppRepository";
import {AppState} from "../../../shared/AppState";

export class SpecificState {
    apps: AppApiOut_type[]|null = null;

    @AutoWired(AppRepository) accessor appsRepository!: AppRepository;
    @AutoWired(AppState) accessor appState!: AppState;

    async init() {
        if(this.apps === null) this.apps = (await this.appsRepository.callContract('list', {establishmentUid: this.appState.requireUrlEstablishmentUid()}, undefined)).success().list;
    }

    requireApps() {
        if(this.apps === null) throw new Error('apps_not_loaded');
        return this.apps;
    }

    haveReservitApp() {
        return this.requireApps().find((app) => app.type === AppType.RESERVIT) !== undefined;
    }
}
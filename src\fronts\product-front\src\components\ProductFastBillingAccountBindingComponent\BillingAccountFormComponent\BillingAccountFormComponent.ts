import {Component, Prop, Vue} from "vue-facing-decorator";
import {FormModalOrDrawerComponent} from "@groupk/vue3-interface-sdk";
import {
    BillingAccountCodeCreationApiIn,
    ProductBillingAccountHttpContract,
    ProductRevisionNotDetailedApiOut
} from "@groupk/mastodon-core";
import {BillingAccountRepository} from "../../../../../../shared/repositories/BillingAccountRepository";
import {AutoWired} from "@groupk/horizon2-core";
import {AppState} from "../../../../../../shared/AppState";
import {translateResponseError} from "../../../../../../shared/RepositoryExtensions";

@Component({
    components: {
        'form-modal-or-drawer': FormModalOrDrawerComponent
    },
    emits: ['created', 'close']
})
export default class BillingAccountFormComponent extends Vue {
    customValue: string = '';
    opened: boolean = false;
    loading: boolean = false;
    error: string|null = null;

    @AutoWired(BillingAccountRepository) accessor billingAccountRepository!: BillingAccountRepository;
    @AutoWired(AppState) accessor appState!: AppState;

    mounted() {
        setTimeout(() => this.opened = true, 0);
    }

    close() {
        this.opened = false;
        setTimeout(() => this.$emit('close'), 300);
    }

    async create() {
        const sanitizedValue = this.customValue.trim();

        if (!sanitizedValue) {
            this.error = "Le compte de classe ne peut pas être vide";
            return;
        }

        const maxLength = BillingAccountCodeCreationApiIn.__entityDefinition?.getFieldString('code')?.maxLength ?? 1000;
        if(sanitizedValue.length > maxLength) {
            this.error = `Le compte de classe ne peut pas être plus long que ${maxLength} caractères`;
            return;
        }

        this.error = null;
        this.loading = true;

        // Emit the created event with the billing account value
        const response = await this.billingAccountRepository.callContract('createCode', {establishmentUid: this.appState.requireUrlEstablishmentUid()}, new BillingAccountCodeCreationApiIn({
            name: sanitizedValue,
            code: sanitizedValue
        }));

        if(response.isSuccess()) {
            this.$emit('created', response.success().item);
        } else {
            this.error = translateResponseError<typeof ProductBillingAccountHttpContract, 'createCode'>(response, {
                invalid_data: undefined,
            });
        }

        this.loading = false;
        this.close();
    }
}

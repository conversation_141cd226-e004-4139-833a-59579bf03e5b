<script lang="ts" src="./BillingAccountFormComponent.ts" />

<style lang="sass">
@import './BillingAccountFormComponent.scss'
</style>

<template>
    <div class="billing-account-form-component">
        <form-modal-or-drawer
            :state="opened"
            title="Créer un compte de classe"
            subtitle="Créer un compte de classe et l'ajouter au produit"
            @close="close()"
        >
            <template v-slot:content>
                <div class="input-group">
                    <label> Compte de classe </label>
                    <input type="text" v-model="customValue" />
                </div>

                <div class="form-error" v-if="error">
                    <i class="fa-solid fa-exclamation-circle"></i>
                    <div class="details">
                        <span class="title"> Erreur </span>
                        <span class="description"> {{ error }} </span>
                    </div>
                </div>
            </template>

            <template v-slot:buttons>
                <button class="white button" @click="close()"> Annuler </button>
                <button class="button" :class="{loading: loading, disabled: loading}" @click="create()"> Valider </button>
            </template>
        </form-modal-or-drawer>
    </div>
</template>

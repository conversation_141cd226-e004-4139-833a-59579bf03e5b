import {Component, Prop, Vue} from "vue-facing-decorator";
import {FormModalOrDrawerComponent, InputPriceComponent, ToggleComponent} from "@groupk/vue3-interface-sdk";
import {AutoWired, UuidUtils, VisualScopedUuid} from "@groupk/horizon2-core";
import {OrderRefundRepository} from "../../../../../shared/repositories/OrderRefundRepository";
import {
    OrderDependenciesApiOut,
    OrderExecutorModel, OrderRefundApiIn, OrderRefundHttpContract,
    PurchaseApiOut, StockMovementApiIn, UuidScopeProduct_stockSimple, uuidScopeProduct_stockSimple,
    UuidScopeProductProductRevision
} from "@groupk/mastodon-core";
import {AppState} from "../../../../../shared/AppState";
import {PaymentMethodDataWeb} from "@groupk/mastodon-core";
import {TicketRepository} from "../../../../../shared/repositories/TicketRepository";
import {StockRepository} from "../../../../../shared/repositories/StockRepository";
import {AppBus} from "../../AppBus";
import {translateResponseError} from "../../../../../shared/RepositoryExtensions";

@Component({
    components: {
        'form-modal-or-drawer': FormModalOrDrawerComponent,
        'toggle': ToggleComponent,
        'price-input': InputPriceComponent,
    }
})
export default class OrderRefundComponent extends Vue {
    @Prop({required: true}) fullOrder!: OrderDependenciesApiOut;
    @Prop({required: true}) orderExecutorModel!: OrderExecutorModel;

    selectedPurchases: PurchaseApiOut[] = [];

    showRefundValidationModal: boolean = false;
    showCustomValueInput: boolean = false;

    comment: string = '';
    cancelTickets: boolean = false;
    putTicketsBackInStock: boolean = false;

    refundingAmount: number = 0;

    refundError: string|null = null;
    opened: boolean = false;

    @AutoWired(OrderRefundRepository) accessor orderRefundRepository!: OrderRefundRepository;
    @AutoWired(StockRepository) accessor stockRepository!: StockRepository;
    @AutoWired(TicketRepository) accessor ticketRepository!: TicketRepository;
    @AutoWired(AppState) accessor appState!: AppState;
    @AutoWired(AppBus) accessor appBus!: AppBus;

    mounted() {
        setTimeout(() => this.opened = true, 0);
    }

    async processRefund() {
        this.showRefundValidationModal = false;
        const orderExistingWebPaymentData: PaymentMethodDataWeb|null = this.fullOrder.order.payments.find((payment) => payment.parents.length === 0 && payment.methodData && payment.methodData.type === 'WEB')?.methodData as PaymentMethodDataWeb ?? null;

        const refundResponse = await this.orderRefundRepository.callContract('create', {establishmentUid: this.appState.requireUrlEstablishmentUid(), orderUid: this.fullOrder.order.uid}, new OrderRefundApiIn({
            methodData: new PaymentMethodDataWeb({
                onlinePaymentUid: orderExistingWebPaymentData.onlinePaymentUid,
                type: 'WEB'
            }),
            amount: this.refundingAmount,
            comment: this.comment,
        }));

        if(refundResponse.isSuccess()) {
            if(this.fullOrder.order.ticketPurchaseLinkList) {
                for(const purchase of this.selectedPurchases) {
                    for(const purchaseItem of purchase.items) {
                        const purchaseItemTicketLink = this.fullOrder.order.ticketPurchaseLinkList.find((link) => link.purchaseItemUid === purchaseItem.uid);
                        if(!purchaseItemTicketLink) continue;

                        if(this.cancelTickets) {
                            await this.ticketRepository.callContract('disable', {establishmentUid: this.appState.requireUrlEstablishmentUid(), ticketUid: purchaseItemTicketLink.ticketUid}, undefined);
                        }

                        if(
                            this.putTicketsBackInStock &&
                            this.requireProductRevisionWithUid(purchase.productRevisionUid).stockUid &&
                            UuidUtils.isVisual(this.requireProductRevisionWithUid(purchase.productRevisionUid).stockUid, uuidScopeProduct_stockSimple)
                        ) {
                            await this.stockRepository.callContract('createStockMovementWithoutInventory', {
                                establishmentUid: this.appState.requireUrlEstablishmentUid(),
                                stockUid: this.requireProductRevisionWithUid(purchase.productRevisionUid).stockUid as VisualScopedUuid<UuidScopeProduct_stockSimple>
                            }, new StockMovementApiIn({
                                stockUid: this.requireProductRevisionWithUid(purchase.productRevisionUid).stockUid as VisualScopedUuid<UuidScopeProduct_stockSimple>,
                                quantity: purchaseItem.quantity,
                                set: false
                            }));
                        }
                    }
                }
            }

            this.close();

            this.appBus.emit('emit-toast', {
                title: 'Demande de remboursement créée',
                description: 'La demande sera traitée très rapidement',
                duration: 3000,
                type: 'SUCCESS',
                closable: true
            })
        } else {
            this.refundError = translateResponseError<typeof OrderRefundHttpContract, 'create'>(refundResponse, {});
        }
    }

    togglePurchase(purchase: PurchaseApiOut) {
        const index = this.selectedPurchases.findIndex((purchaseUid) => purchaseUid.uid === purchase.uid);
        if(index === -1) {
            this.selectedPurchases.push(purchase);
            if(!this.showCustomValueInput) {
                this.refundingAmount += this.orderExecutorModel.getPurchasePrice(this.fullOrder.order, purchase).withTaxes;
            }
        } else {
            this.selectedPurchases.splice(index, 1);
            if(!this.showCustomValueInput) {
                this.refundingAmount -= this.orderExecutorModel.getPurchasePrice(this.fullOrder.order, purchase).withTaxes;
            }
        }
    }

    isPurchaseSelected(purchase: PurchaseApiOut) {
        return this.selectedPurchases.findIndex((purchaseUid) => purchaseUid.uid === purchase.uid) !== -1;
    }

    getLeftToRefundAmount() {
        return this.orderExecutorModel.getOrderTotals(this.fullOrder.order).payments.total - this.fullOrder.onlinePaymentRefundList.reduce((total, refund) => total + refund.amount, 0);
    }

    close() {
        this.opened = false;
        setTimeout(() => this.$emit('close'), 300);
    }

    requireProductRevisionWithUid(productRevisionUid: VisualScopedUuid<UuidScopeProductProductRevision>) {
        const productRevision = this.fullOrder.productRevisions.find((revision) => revision.uid === productRevisionUid);
        if (!productRevision) throw new Error('missing_product_revision');
        return productRevision;
    }
}
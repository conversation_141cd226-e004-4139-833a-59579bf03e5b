/**
 * Generic HTTP error responses for API contracts
 * These are standard error responses that can be used across different APIs
 */

export const HttpGeneric = {
    /**
     * 404 Not Found error
     */
    NotFound: {
        code: 404,
        body: {
            error: 'Not Found',
            message: 'The requested resource was not found'
        }
    },

    /**
     * 400 Bad Request error
     */
    BadRequest: {
        code: 400,
        body: {
            error: 'Bad Request',
            message: 'The request was invalid'
        }
    },

    /**
     * 422 Validation Error
     */
    ValidationError: {
        code: 422,
        body: {
            error: 'Validation Error',
            message: 'The request data failed validation',
            details: {}
        }
    },

    /**
     * 401 Unauthorized error
     */
    Unauthorized: {
        code: 401,
        body: {
            error: 'Unauthorized',
            message: 'Authentication is required'
        }
    },

    /**
     * 403 Forbidden error
     */
    Forbidden: {
        code: 403,
        body: {
            error: 'Forbidden',
            message: 'Access to this resource is forbidden'
        }
    },

    /**
     * 500 Internal Server Error
     */
    InternalServerError: {
        code: 500,
        body: {
            error: 'Internal Server Error',
            message: 'An internal server error occurred'
        }
    },

    /**
     * 429 Too Many Requests error
     */
    TooManyRequests: {
        code: 429,
        body: {
            error: 'Too Many Requests',
            message: 'Rate limit exceeded'
        }
    },

    /**
     * 409 Conflict error
     */
    Conflict: {
        code: 409,
        body: {
            error: 'Conflict',
            message: 'The request conflicts with the current state'
        }
    }
} as const;

/**
 * Baserow-specific error responses
 */
export const BaserowGeneric = {
    /**
     * Baserow table not found
     */
    TableNotFound: {
        code: 404,
        body: {
            error: 'Table Not Found',
            message: 'The specified Baserow table was not found'
        }
    },

    /**
     * Baserow row not found
     */
    RowNotFound: {
        code: 404,
        body: {
            error: 'Row Not Found',
            message: 'The specified row was not found in the table'
        }
    },

    /**
     * Invalid Baserow token
     */
    InvalidToken: {
        code: 401,
        body: {
            error: 'Invalid Token',
            message: 'The provided Baserow token is invalid or expired'
        }
    },

    /**
     * Baserow field validation error
     */
    FieldValidationError: {
        code: 400,
        body: {
            error: 'Field Validation Error',
            message: 'One or more fields contain invalid data'
        }
    }
} as const;

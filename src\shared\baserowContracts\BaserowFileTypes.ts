import {
    Entity,
    EntityClass,
    EntityAsSimpleObject,
    StringField,
    IntegerField,
    BoolField,
    EntityField
} from "@groupk/horizon2-core";

/**
 * Baserow file thumbnail interface
 */
@EntityClass()
export class BaserowFileThumbnail extends Entity {
    /** Thumbnail URL */
    @StringField() url: string;
    
    /** Thumbnail width (can be null) */
    @IntegerField({nullable: true}) width: number | null;
    
    /** Thumbnail height */
    @IntegerField() height: number;

    constructor({
        url,
        width,
        height
    }: EntityAsSimpleObject<BaserowFileThumbnail>) {
        super();
        this.url = url;
        this.width = width;
        this.height = height;
    }
}

/**
 * Baserow file thumbnails collection
 */
@EntityClass()
export class BaserowFileThumbnails extends Entity {
    /** Tiny thumbnail (usually very small) */
    @EntityField(BaserowFileThumbnail) tiny: BaserowFileThumbnail;
    
    /** Small thumbnail (usually 48x48) */
    @EntityField(BaserowFileThumbnail) small: BaserowFileThumbnail;
    
    /** Card cover thumbnail (usually 300x160) */
    @EntityField(BaserowFileThumbnail) card_cover: BaserowFileThumbnail;

    constructor({
        tiny,
        small,
        card_cover
    }: EntityAsSimpleObject<BaserowFileThumbnails>) {
        super();
        this.tiny = tiny;
        this.small = small;
        this.card_cover = card_cover;
    }
}

/**
 * Baserow file object interface
 * Represents a file uploaded to Baserow with all its metadata
 */
@EntityClass()
export class BaserowFile extends Entity {
    /** Full file URL */
    @StringField() url: string;
    
    /** File thumbnails (for images) */
    @EntityField(BaserowFileThumbnails, {nullable: true}) thumbnails: BaserowFileThumbnails|null;
    
    /** Human-readable file name */
    @StringField() visible_name: string;
    
    /** Internal file name in storage */
    @StringField() name: string;
    
    /** File size in bytes */
    @IntegerField() size: number;
    
    /** MIME type */
    @StringField() mime_type: string;
    
    /** Whether this is an image file */
    @BoolField() is_image: boolean;
    
    /** Image width (for images) */
    @IntegerField({nullable: true}) image_width: number | null;
    
    /** Image height (for images) */
    @IntegerField({nullable: true}) image_height: number | null;
    
    /** Upload timestamp */
    @StringField() uploaded_at: string;

    constructor({
        url,
        thumbnails,
        visible_name,
        name,
        size,
        mime_type,
        is_image,
        image_width,
        image_height,
        uploaded_at
    }: EntityAsSimpleObject<BaserowFile>) {
        super();
        this.url = url;
        this.thumbnails = thumbnails;
        this.visible_name = visible_name;
        this.name = name;
        this.size = size;
        this.mime_type = mime_type;
        this.is_image = is_image;
        this.image_width = image_width;
        this.image_height = image_height;
        this.uploaded_at = uploaded_at;
    }
}

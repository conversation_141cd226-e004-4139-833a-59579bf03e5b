import {
    ExpectEntity,
    validateHttpRouteDescriptorAggregate,
    HttpRouteDescriptorArgumentExpectUnsignedInteger,
    HttpRouteDescriptorArgumentExpectString, HttpRouteDescriptorAggregate,
} from '@groupk/horizon2-core';
import { ObjectNamesApiIn, ObjectNamesApiOut, ObjectNamesListApiOut } from './ObjectNamesApiClasses';
import { HttpGeneric } from '../../GenericResponses';

/**
 * HTTP Contract for Baserow ObjectNames table operations
 * Table ID: 1733
 * Base URL: https://baserow-api.lab.weecop.fr/api/database/rows/table/1733/
 */
export const BaserowObjectNamesContractAggregate = validateHttpRouteDescriptorAggregate({
    /**
     * Get a single object name by ID
     */
    getOne: {
        method: 'GET',
        path: '/api/database/rows/table/1733/{{objectNameId}}/',
        arguments: {
            objectNameId: HttpRouteDescriptorArgumentExpectUnsignedInteger(),
        },
        returns: [
            { code: 200, body: ExpectEntity(ObjectNamesApiOut) },
        ],
        errors: [
            HttpGeneric.NotFound,
            HttpGeneric.BadRequest,
        ],
    },

    /**
     * List object names with optional pagination and filtering
     */
    list: {
        method: 'GET',
        path: '/api/database/rows/table/1733/',
        returns: [
            { code: 200, body: ExpectEntity(ObjectNamesListApiOut) },
        ],
        errors: [
            HttpGeneric.BadRequest,
        ],
    },

    /**
     * Create a new object name
     */
    create: {
        method: 'POST',
        path: '/api/database/rows/table/1733/',
        expected: ExpectEntity(ObjectNamesApiIn),
        returns: [
            { code: 200, body: ExpectEntity(ObjectNamesApiOut) },
            { code: 201, body: ExpectEntity(ObjectNamesApiOut) },
        ],
        errors: [
            HttpGeneric.BadRequest,
            HttpGeneric.ValidationError,
        ],
    },

    /**
     * Update an existing object name
     */
    update: {
        method: 'PATCH',
        path: '/api/database/rows/table/1733/{{objectNameId}}/',
        arguments: {
            objectNameId: HttpRouteDescriptorArgumentExpectUnsignedInteger(),
        },
        expected: ExpectEntity(ObjectNamesApiIn),
        returns: [
            { code: 200, body: ExpectEntity(ObjectNamesApiOut) },
        ],
        errors: [
            HttpGeneric.NotFound,
            HttpGeneric.BadRequest,
            HttpGeneric.ValidationError,
        ],
    },

    /**
     * Delete an object name
     */
    delete: {
        method: 'DELETE',
        path: '/api/database/rows/table/1733/{{objectNameId}}/',
        arguments: {
            objectNameId: HttpRouteDescriptorArgumentExpectUnsignedInteger(),
        },
        returns: [
            { code: 204, body: null },
        ],
        errors: [
            HttpGeneric.NotFound,
            HttpGeneric.BadRequest,
        ],
    },
} as const satisfies HttpRouteDescriptorAggregate);

import {
    Entity,
    EntityClass,
    EntityAsSimpleObject,
    IntegerField,
    StringField,
    EntityField
} from "@groupk/horizon2-core";

/**
 * Brand API class for brand objects returned from Baserow
 * Represents the full brand object with id, value, and color
 */
@EntityClass()
export class Brand<PERSON><PERSON> extends Entity {
    @IntegerField() id: number;
    @StringField() value: string;
    @StringField() color: string;

    constructor({
        id,
        value,
        color
    }: EntityAsSimpleObject<BrandApi>) {
        super();
        this.id = id;
        this.value = value;
        this.color = color;
    }
}

/**
 * Brand options for the brand field in ObjectNames
 * These correspond to the single select options in Baserow
 */
export const BRAND_OPTIONS = Object.freeze({
    SUNMI: 7485,
    JONKUU: 7486,
    QUILIVE: 7487,
    PAX: 7488
} as const);

export const BRAND_LABELS = {
    [BRAND_OPTIONS.SUNMI]: 'Sunmi',
    [BRAND_OPTIONS.JONKUU]: '<PERSON><PERSON><PERSON>',
    [BRAND_OPTIONS.QUILIVE]: 'Quilive',
    [BRAND_OPTIONS.PAX]: 'PAX'
} as const;

export type BrandOption = typeof BRAND_OPTIONS[keyof typeof BRAND_OPTIONS];

/**
 * ObjectNames API Input class for creating/updating object names in Baserow
 * Based on ObjectNamesEntity structure for Baserow Table ID: 1733
 */
@EntityClass()
export class ObjectNamesApiIn extends Entity {
    /** field_16710: Object code */
    @StringField() code: string;

    /** field_16709: Object name */
    @StringField() name: string;

    /** field_16711: Brand selection (for input, send the brand ID as number) */
    @IntegerField() brand: number;

    constructor({
        code,
        name,
        brand
    }: EntityAsSimpleObject<ObjectNamesApiIn>) {
        super();
        this.code = code;
        this.name = name;
        this.brand = brand;
    }
}

/**
 * ObjectNames API Output class for responses from Baserow
 * Based on ObjectNamesEntity structure for Baserow Table ID: 1733
 */
@EntityClass()
export class ObjectNamesApiOut extends Entity {
    @IntegerField() id: number;

    /** field_16710: Object code */
    @StringField() code: string;

    /** field_16709: Object name */
    @StringField() name: string;

    /** field_16711: Brand selection (returns full brand object with id, value, color) */
    @EntityField(BrandApi, {nullable: true}) brand: BrandApi|null;

    constructor({
        id,
        code,
        name,
        brand
    }: EntityAsSimpleObject<ObjectNamesApiOut>) {
        super();
        this.id = id;
        this.code = code;
        this.name = name;
        this.brand = brand;
    }
}

/**
 * Baserow list response wrapper for ObjectNames entities
 */
@EntityClass()
export class ObjectNamesListApiOut extends Entity {
    @IntegerField() count: number;
    @StringField({nullable: true}) next: string | null;
    @StringField({nullable: true}) previous: string | null;
    @EntityField(ObjectNamesApiOut, {array: true}) results: ObjectNamesApiOut[];

    constructor({
        count,
        next,
        previous,
        results
    }: EntityAsSimpleObject<ObjectNamesListApiOut>) {
        super();
        this.count = count;
        this.next = next;
        this.previous = previous;
        this.results = results;
    }
}

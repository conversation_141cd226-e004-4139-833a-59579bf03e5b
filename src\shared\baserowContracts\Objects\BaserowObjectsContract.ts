import {
    ExpectEntity,
    validateHttpRouteDescriptorAggregate,
    HttpRouteDescriptorArgumentExpectUnsignedInteger,
    HttpRouteDescriptorAggregate, HttpRouteResponseCode,
} from '@groupk/horizon2-core';
import { ObjectApiIn, ObjectApiOut, ObjectListApiOut } from './ObjectsApiClasses';
import { HttpGeneric } from '../../GenericResponses';

/**
 * HTTP Contract for Baserow Objects table operations
 * Table ID: 1732
 * Base URL: https://baserow-api.lab.weecop.fr/api/database/rows/table/1732/
 */
export const BaserowObjectsContractAggregate = validateHttpRouteDescriptorAggregate({
    /**
     * Get a single object by ID
     */
    getOne: {
        method: 'GET',
        path: '/api/database/rows/table/1732/{{objectId}}/',
        arguments: {
            objectId: HttpRouteDescriptorArgumentExpectUnsignedInteger(),
        },
        returns: [
            { code: 200, body: ExpectEntity(ObjectApiOut) },
        ],
        errors: [
            HttpGeneric.NotFound,
            HttpGeneric.BadRequest,
        ],
    },

    /**
     * List objects with optional pagination and filtering
     */
    list: {
        method: 'GET',
        path: '/api/database/rows/table/1732/',
        returns: [
            { code: 200, body: ExpectEntity(ObjectListApiOut) },
        ],
        errors: [
            HttpGeneric.BadRequest,
        ],
    },

    /**
     * Create a new object
     */
    create: {
        method: 'POST',
        path: '/api/database/rows/table/1732/',
        expected: ExpectEntity(ObjectApiIn),
        returns: [
            { code: 200, body: ExpectEntity(ObjectApiOut) },
            { code: 201, body: ExpectEntity(ObjectApiOut) },
        ],
        errors: [
            HttpGeneric.BadRequest,
            HttpGeneric.ValidationError,
        ],
    },

    /**
     * Update an existing object
     */
    update: {
        method: 'PATCH',
        path: '/api/database/rows/table/1732/{{objectId}}/',
        arguments: {
            objectId: HttpRouteDescriptorArgumentExpectUnsignedInteger(),
        },
        expected: ExpectEntity(ObjectApiIn),
        returns: [
            { code: 200, body: ExpectEntity(ObjectApiOut) },
        ],
        errors: [
            HttpGeneric.NotFound,
            HttpGeneric.BadRequest,
            HttpGeneric.ValidationError,
        ],
    },

    /**
     * Delete an object
     */
    delete: {
        method: 'DELETE',
        path: '/api/database/rows/table/1732/{{objectId}}/',
        arguments: {
            objectId: HttpRouteDescriptorArgumentExpectUnsignedInteger(),
        },
        returns: [
            { code: HttpRouteResponseCode.noContent, body: undefined },
        ],
        errors: [
            HttpGeneric.NotFound,
            HttpGeneric.BadRequest,
        ],
    },
} as const satisfies HttpRouteDescriptorAggregate);

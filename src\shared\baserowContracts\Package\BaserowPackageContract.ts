import {
    ExpectEntity,
    validateHttpRouteDescriptorAggregate,
    HttpRouteDescriptorArgumentExpectUnsignedInteger, HttpRouteDescriptorAggregate,
} from '@groupk/horizon2-core';
import { PackageApiIn, PackageApiOut, PackageListApiOut } from './PackageApiClasses';
import { HttpGeneric } from '../../GenericResponses';

/**
 * HTTP Contract for Baserow Package table operations
 * Table ID: 1731
 * Base URL: https://baserow-api.lab.weecop.fr/api/database/rows/table/1731/
 */
export const BaserowPackageContractAggregate = validateHttpRouteDescriptorAggregate({
    /**
     * Get a single package by ID
     */
    getOne: {
        method: 'GET',
        path: '/api/database/rows/table/1731/{{packageId}}/',
        arguments: {
            packageId: HttpRouteDescriptorArgumentExpectUnsignedInteger(),
        },
        returns: [
            { code: 200, body: ExpectEntity(PackageApiOut) },
        ],
        errors: [
            HttpGeneric.NotFound,
            HttpGeneric.BadRequest,
        ],
    },

    /**
     * List packages with optional pagination and filtering
     */
    list: {
        method: 'GET',
        path: '/api/database/rows/table/1731/',
        returns: [
            { code: 200, body: ExpectEntity(PackageListApiOut) },
        ],
        errors: [
            HttpGeneric.BadRequest,
        ],
    },

    /**
     * Create a new package
     */
    create: {
        method: 'POST',
        path: '/api/database/rows/table/1731/',
        expected: ExpectEntity(PackageApiIn),
        returns: [
            { code: 200, body: ExpectEntity(PackageApiOut) },
            { code: 201, body: ExpectEntity(PackageApiOut) },
        ],
        errors: [
            HttpGeneric.BadRequest,
            HttpGeneric.ValidationError,
        ],
    },

    /**
     * Update an existing package
     */
    update: {
        method: 'PATCH',
        path: '/api/database/rows/table/1731/{{packageId}}/',
        arguments: {
            packageId: HttpRouteDescriptorArgumentExpectUnsignedInteger(),
        },
        expected: ExpectEntity(PackageApiIn),
        returns: [
            { code: 200, body: ExpectEntity(PackageApiOut) },
        ],
        errors: [
            HttpGeneric.NotFound,
            HttpGeneric.BadRequest,
            HttpGeneric.ValidationError,
        ],
    },

    /**
     * Delete a package
     */
    delete: {
        method: 'DELETE',
        path: '/api/database/rows/table/1731/{{packageId}}/',
        arguments: {
            packageId: HttpRouteDescriptorArgumentExpectUnsignedInteger(),
        },
        returns: [
            { code: 204, body: null },
        ],
        errors: [
            HttpGeneric.NotFound,
            HttpGeneric.BadRequest,
        ],
    },
} as const satisfies HttpRouteDescriptorAggregate);

import {Component, Prop, Vue} from "vue-facing-decorator";
import {DropdownComponent, DropdownValue, FormModalOrDrawerComponent} from "@groupk/vue3-interface-sdk";
import {
    AppReservitApiOut,
    AppReservitUpdateApiIn,
    ReservitApi_configuration,
    ReservitAppHttpContractAggregate
} from "@groupk/mastodon-core";
import {AutoWired} from "@groupk/horizon2-core";
import {AppState} from "../../AppState";
import {ReservitAppRepository} from "../../repositories/ReservitAppRepository";
import {ReservitApi_optionCategoryId} from "@groupk/mastodon-core";
import {translateResponseError} from "../../RepositoryExtensions";

@Component({
    components: {
        'form-modal-or-drawer': FormModalOrDrawerComponent,
        'dropdown': DropdownComponent
    }
})
export default class CategoryReservitBindingComponent extends Vue {
    @Prop({required: true}) reservitApp!: AppReservitApiOut;

    reservitConfiguration!: ReservitApi_configuration;

    selectedCategoryId: ReservitApi_optionCategoryId|null = null;

    error: string|null = null;
    loading: boolean = true;
    saving: boolean = false;
    opened: boolean = false;

    @AutoWired(ReservitAppRepository) accessor reservitAppRepository!: ReservitAppRepository;
    @AutoWired(AppState) accessor appState!: AppState;

    async mounted() {
        setTimeout(() => this.opened = true, 0);

        this.reservitConfiguration = (await this.reservitAppRepository.callContract('getConfiguration', {
            establishmentUid: this.appState.requireUrlEstablishmentUid(),
            appUid: this.reservitApp.uid
        }, undefined)).success();

        this.selectedCategoryId = this.reservitApp.productCategoryId;

        this.loading = false;
    }

    categoriesDropdownValues(): DropdownValue[] {
        return this.reservitConfiguration.optionCategories.map((category) => {
            return {
                name: category.label,
                value: category.id
            }
        })
    }

    async updateReservitAppCategory() {
        this.saving = true;

        const response = (await this.reservitAppRepository.callContract('updateApp', {
            establishmentUid: this.appState.requireUrlEstablishmentUid(),
            appUid: this.reservitApp.uid
        }, new AppReservitUpdateApiIn({
            name: this.reservitApp.name,
            token: undefined,
            platform: this.reservitApp.platform,
            enableAutoCreateProduct: this.reservitApp.enableAutoCreateProduct,
            productCategoryId: this.selectedCategoryId
        })));

        if(response.isSuccess()) {
            this.$emit('updated-app', response.success());
            this.close();
        } else {
            this.error = translateResponseError<typeof ReservitAppHttpContractAggregate, 'updateApp'>(response, {});
        }

        this.saving = false;
    }

    close() {
        this.opened = false;
        setTimeout(() => this.$emit('close'), 300);
    }
}
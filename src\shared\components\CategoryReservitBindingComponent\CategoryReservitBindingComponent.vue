<script lang="ts" src="./CategoryReservitBindingComponent.ts"/>

<style lang="sass" scoped>
@import './CategoryReservitBindingComponent.scss'
</style>

<template>
    <div class="category-reservit-binding-component">
        <form-modal-or-drawer
            :state="opened"
            title="Choisir une catégorie Reservit"
            subtitle="Choisissez la catégorie dans laquelle seront créés les produits dans Reservit"
            @close="close()"
        >
            <template v-slot:content>
                <div v-if="loading" class="loading-container">
                    <div class="loader"></div>
                </div>

                <template v-else>
                    <div class="input-group">
                        <label> Catégorie </label>
                        <dropdown
                            placeholder="Catégorie Reservit"
                            :values="categoriesDropdownValues()"
                            :default-selected="selectedCategoryId"
                            @update="selectedCategoryId = $event"
                        ></dropdown>
                    </div>

                    <div class="form-error" v-if="error">
                        <i class="fa-solid fa-exclamation-circle"></i>
                        <div class="details">
                            <span class="title"> Erreur </span>
                            <span class="description"> {{ error }} </span>
                        </div>
                    </div>
                </template>
            </template>
            <template v-slot:buttons>
                <button type="button" class="white button" @click="close()"> Annuler </button>
                <button type="button" class="button" :class="{loading: saving, disabled: saving}" @click="updateReservitAppCategory()"> Valider </button>
            </template>
        </form-modal-or-drawer>
    </div>
</template>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Stocks Label Generator</title>
    <style>
        @page {
            size: 210mm 297mm;
            padding: 0;
            margin: 0;
        }

        html {
            width: 210mm;
            height:  297mm;
        }

        body {
            margin: 0;
            box-sizing: border-box;
            width: 210mm;
        }

        /* Input view styles */
        .input-view {
            padding: 20px;
            max-width: 800px;
            margin: 0 auto;
        }

        .input-view h1 {
            text-align: center;
            color: #333;
        }

        .input-view textarea {
            width: 100%;
            height: 300px;
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
            resize: vertical;
        }

        .input-view button {
            background-color: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            margin-top: 10px;
        }

        .input-view button:hover {
            background-color: #0056b3;
        }

        .input-view .instructions {
            margin-bottom: 15px;
            color: #666;
            line-height: 1.5;
        }

        /* Print view styles */
        .print-view {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            grid-template-rows: 1fr 1fr 1fr;
            grid-gap: 17mm 2mm;
            padding: 5mm 5mm;
            width: 210mm;
            height: 297mm;
            box-sizing: border-box;
        }

        .place {
            position: relative;
        }

        .qrcode {
            height: 10mm;
            width: 10mm;
        }

        .qrcode1 {
            position: absolute;
            top: 2mm;
            left: 15mm;
        }

        .qrcode2 {
            position: absolute;
            top: 2mm;
            left: 27mm;
        }

        .qrcode3 {
            position: absolute;
            top: 72mm;
            left: 2mm;
        }

        .qrcode4 {
            position: absolute;
            top: 72mm;
            left: 14mm;
        }

        .text {
            font-size: 3mm;
        }

        .text1 {
            position: absolute;
            top: 13mm;
            left: 15mm;
        }

        .text2 {
            position: absolute;
            top: 13mm;
            left: 27mm;
        }

        .text3 {
            position: absolute;
            top: 68mm;
            left: 2mm;
        }

        .text4 {
            position: absolute;
            top: 68mm;
            left: 14mm;
        }

        .hidden {
            display: none;
        }

        .back-button {
            position: fixed;
            top: 10px;
            left: 10px;
            background-color: #6c757d;
            color: white;
            padding: 8px 16px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            z-index: 1000;
        }

        .back-button:hover {
            background-color: #545b62;
        }

        @media print {
            .back-button {
                display: none;
            }
        }
    </style>
</head>
<body>
<!-- Input View -->
<div id="inputView" class="input-view">
    <h1>Stocks Label Generator</h1>
    <div class="instructions">
        <p>Paste up to 30 QR code values below, separated by new lines or commas. Each value will generate 2 QR codes (60 total), with pairs sharing the same QR code.</p>
    </div>
    <textarea id="qrInput" placeholder="Paste your QR code values here, one per line or separated by commas..."></textarea>
    <br>
    <button onclick="generateLabels()">Generate Labels</button>
</div>

<!-- Print View -->
<div id="printView" class="print-view hidden">
    <button class="back-button" onclick="goBack()">← Back to Input</button>
    <!-- Labels will be dynamically generated here -->
</div>

<script>
    function parseValues(input) {
        // Split by newlines first, then by commas, and clean up
        return input
            .split(/[\n,]/)
            .map(value => value.trim())
            .filter(value => value.length > 0)
            .slice(0, 30); // Limit to 30 values
    }

    function generateLabels() {
        const input = document.getElementById('qrInput').value;
        const qrValues = parseValues(input);

        if (qrValues.length === 0) {
            alert('Please enter at least one QR code value.');
            return;
        }

        const printView = document.getElementById('printView');
        const inputView = document.getElementById('inputView');

        // Clear previous content
        printView.innerHTML = '<button class="back-button" onclick="goBack()">← Back to Input</button>';

        // Calculate how many complete sets of 15 labels we need
        const labelsPerPage = 15; // 5 columns × 3 rows
        const totalPages = Math.ceil(qrValues.length / (labelsPerPage * 2)); // 2 pairs of QR codes per label

        let valueIndex = 0;

        for (let page = 0; page < totalPages; page++) {
            for (let labelIndex = 0; labelIndex < labelsPerPage && valueIndex < qrValues.length; labelIndex++) {
                const place = document.createElement('div');
                place.className = 'place';

                // Add 2 pairs of QR codes per label (4 total QR codes using 2 values)
                if (valueIndex < qrValues.length) {
                    // First pair: qrcode1 and qrcode2 use the same value
                    const firstValue = qrValues[valueIndex];

                    const img1 = document.createElement('img');
                    img1.className = 'qrcode qrcode1';
                    img1.src = `https://api.qrserver.com/v1/create-qr-code/?size=150x150&data=${encodeURIComponent(firstValue)}`;
                    img1.alt = `QR Code for ${firstValue}`;
                    place.appendChild(img1);

                    const img2 = document.createElement('img');
                    img2.className = 'qrcode qrcode2';
                    img2.src = `https://api.qrserver.com/v1/create-qr-code/?size=150x150&data=${encodeURIComponent(firstValue)}`;
                    img2.alt = `QR Code for ${firstValue}`;
                    place.appendChild(img2);

                    const text1 = document.createElement('div');
                    text1.className = 'text text1';
                    text1.textContent = firstValue;
                    place.appendChild(text1);

                    const text2 = document.createElement('div');
                    text2.className = 'text text2';
                    text2.textContent = firstValue;
                    place.appendChild(text2);

                    valueIndex++;
                }

                if (valueIndex < qrValues.length) {
                    // Second pair: qrcode3 and qrcode4 use the same value
                    const secondValue = qrValues[valueIndex];

                    const img3 = document.createElement('img');
                    img3.className = 'qrcode qrcode3';
                    img3.src = `https://api.qrserver.com/v1/create-qr-code/?size=150x150&data=${encodeURIComponent(secondValue)}`;
                    img3.alt = `QR Code for ${secondValue}`;
                    place.appendChild(img3);

                    const img4 = document.createElement('img');
                    img4.className = 'qrcode qrcode4';
                    img4.src = `https://api.qrserver.com/v1/create-qr-code/?size=150x150&data=${encodeURIComponent(secondValue)}`;
                    img4.alt = `QR Code for ${secondValue}`;
                    place.appendChild(img4);

                    const text3 = document.createElement('div');
                    text3.className = 'text text3';
                    text3.textContent = secondValue;
                    place.appendChild(text3);

                    const text4 = document.createElement('div');
                    text4.className = 'text text4';
                    text4.textContent = secondValue;
                    place.appendChild(text4);

                    valueIndex++;
                }

                printView.appendChild(place);
            }
        }

        // Switch views
        inputView.classList.add('hidden');
        printView.classList.remove('hidden');
    }

    function goBack() {
        const printView = document.getElementById('printView');
        const inputView = document.getElementById('inputView');

        printView.classList.add('hidden');
        inputView.classList.remove('hidden');
    }
</script>
</body>
</html>